#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话文件大小统计功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.domain.services.knowledge_service import knowledgebase_service
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.repositories.kb_documents_repository import kb_documents_repository


def test_batch_sum_file_size_by_session_ids():
    """测试批量统计会话文件大小功能"""
    print("=== 测试批量统计会话文件大小功能 ===")
    
    # 测试参数
    kb_id = "test_kb_001"
    session_ids = ["session_001", "session_002", "session_003"]
    
    try:
        # 调用批量统计方法
        file_sizes = kb_documents_repository.batch_sum_file_size_by_session_ids(
            kb_id=kb_id,
            session_ids=session_ids
        )
        
        print(f"知识库ID: {kb_id}")
        print(f"会话ID列表: {session_ids}")
        print(f"文件大小统计结果: {file_sizes}")
        
        # 验证结果
        for session_id in session_ids:
            size = file_sizes.get(session_id, 0)
            print(f"会话 {session_id} 的文件大小: {size} bytes")
            
        print("✅ 批量统计会话文件大小功能测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise


def test_list_sessions_with_file_size():
    """测试会话列表中的文件大小统计"""
    print("\n=== 测试会话列表中的文件大小统计 ===")
    
    # 模拟认证上下文
    auth_context = AuthContext(
        ali_uid=123456,
        wy_id="test_user_001",
        token="test_token"
    )
    
    # 测试参数
    kb_id = "test_kb_001"
    max_results = 10
    next_token = ""
    
    try:
        # 调用会话列表方法
        response = knowledgebase_service.list_sessions(
            auth_context=auth_context,
            kb_id=kb_id,
            max_results=max_results,
            next_token=next_token
        )
        
        print(f"知识库ID: {kb_id}")
        print(f"返回会话数量: {len(response.data)}")
        print(f"总会话数量: {response.max_results}")
        
        # 验证每个会话的文件大小
        for session in response.data:
            print(f"会话 {session.session_id}:")
            print(f"  - 会话名称: {session.session_name}")
            print(f"  - 文件大小: {session.file_size} bytes")
            print(f"  - 状态: {session.status}")
            print(f"  - 创建时间: {session.gmt_created}")
        
        print("✅ 会话列表文件大小统计功能测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        # 如果是权限或数据不存在的错误，这是正常的
        if "权限" in str(e) or "不存在" in str(e):
            print("⚠️  这是预期的错误，因为测试环境中没有真实的数据")
        else:
            raise


if __name__ == "__main__":
    print("开始测试会话文件大小统计功能...")
    
    # 测试批量统计方法
    test_batch_sum_file_size_by_session_ids()
    
    # 测试会话列表方法
    test_list_sessions_with_file_size()
    
    print("\n🎉 所有测试完成！") 