#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试 FileService.get_download_urls 方法
直接调用真实的服务方法
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(__file__))

def test_get_download_urls():
    """测试获取下载链接"""
    try:
        # 导入服务
        from src.domain.services.file_service import file_service
        
        # 测试文件ID
        test_file_id = "artifact-65aedb6d788442ee8d017bf39c7c2e9e"
        file_ids = [test_file_id]
        
        print(f"开始测试获取下载链接...")
        print(f"文件ID: {test_file_id}")
        print("-" * 50)
        
        # 调用方法
        result = file_service.get_download_urls(file_ids, expires=3600)
        
        print(f"测试结果类型: {type(result)}")
        print(f"成功的下载链接数量: {len(result.download_links)}")
        print(f"失败的文件数量: {len(result.failed_files)}")
        print("-" * 50)
        
        # 打印成功的下载链接
        if result.download_links:
            print("成功的下载链接:")
            for i, link in enumerate(result.download_links, 1):
                print(f"  {i}. 文件ID: {link.file_id}")
                print(f"     文件名: {link.file_name}")
                print(f"     文件大小: {link.file_size} bytes")
                print(f"     内容类型: {link.content_type}")
                print(f"     下载链接: {link.download_url}")
                print(f"     过期时间: {link.expires_in} 秒")
                print()
        
        # 打印失败的文件
        if result.failed_files:
            print("失败的文件:")
            for i, failed in enumerate(result.failed_files, 1):
                print(f"  {i}. 文件ID: {failed.file_id}")
                print(f"     文件名: {failed.file_name}")
                print(f"     错误信息: {failed.error}")
                print()
        
        # 测试多个文件ID
        print("=" * 50)
        print("测试多个文件ID（包含不存在的）:")
        multiple_file_ids = [
            "artifact-65aedb6d788442ee8d017bf39c7c2e9e",
            "artifact-nonexistent-123",
            "artifact-another-test"
        ]
        
        result2 = file_service.get_download_urls(multiple_file_ids, expires=1800)
        print(f"成功的下载链接数量: {len(result2.download_links)}")
        print(f"失败的文件数量: {len(result2.failed_files)}")
        
        if result2.failed_files:
            print("失败的文件:")
            for failed in result2.failed_files:
                print(f"  - {failed.file_id}: {failed.error}")
        
        return result
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保在项目根目录下运行此脚本")
        return None
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_with_different_expires():
    """测试不同的过期时间"""
    try:
        from src.domain.services.file_service import file_service
        
        test_file_id = "artifact-65aedb6d788442ee8d017bf39c7c2e9e"
        
        print("\n" + "=" * 50)
        print("测试不同的过期时间:")
        
        expires_times = [300, 1800, 3600, 7200]  # 5分钟, 30分钟, 1小时, 2小时
        
        for expires in expires_times:
            print(f"\n过期时间: {expires} 秒 ({expires//60} 分钟)")
            result = file_service.get_download_urls([test_file_id], expires=expires)
            
            if result.download_links:
                link = result.download_links[0]
                print(f"  生成成功，过期时间: {link.expires_in} 秒")
                # 只显示URL的前50个字符，避免太长
                url_preview = link.download_url[:50] + "..." if len(link.download_url) > 50 else link.download_url
                print(f"  URL预览: {url_preview}")
            else:
                print(f"  生成失败")
                if result.failed_files:
                    print(f"  错误: {result.failed_files[0].error}")
                    
    except Exception as e:
        print(f"测试异常: {e}")


def main():
    """主函数"""
    print("FileService.get_download_urls 方法测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now()}")
    print()
    
    # 基本测试
    result = test_get_download_urls()
    
    # 不同过期时间测试
    if result is not None:
        test_with_different_expires()
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
