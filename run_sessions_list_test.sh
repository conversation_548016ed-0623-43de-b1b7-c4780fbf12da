#!/bin/bash

# 会话列表分页接口测试脚本
# 在venv环境中运行测试

echo "🎯 会话列表分页接口测试"
echo "================================"

# 检查是否在项目根目录
if [ ! -f "pyproject.toml" ]; then
    echo "❌ 错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 检查venv是否存在
if [ ! -d "venv" ]; then
    echo "⚠️  虚拟环境不存在，正在创建..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 检查是否成功激活
if [ -z "$VIRTUAL_ENV" ]; then
    echo "❌ 虚拟环境激活失败"
    exit 1
fi

echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"

# 安装依赖
echo "📦 安装项目依赖..."
pip install -e .

# 检查requests是否已安装
if ! python -c "import requests" 2>/dev/null; then
    echo "📦 安装requests依赖..."
    pip install requests
fi

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "⚠️  服务未运行，请先启动服务:"
    echo "   python start_service.py"
    echo ""
    echo "或者使用以下命令启动服务:"
    echo "   uvicorn src.presentation.api.pythonic_server:app --reload --host 0.0.0.0 --port 8000"
    echo ""
    read -p "是否继续测试? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "测试已取消"
        exit 0
    fi
fi

# 运行测试
echo "🚀 开始运行测试..."
python test_sessions_list_pagination.py

# 测试完成
echo ""
echo "✅ 测试完成"
echo "📝 测试脚本保留在: test_sessions_list_pagination.py" 