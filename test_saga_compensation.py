#!/usr/bin/env python3
"""
测试 Saga 事务的补偿操作功能
"""

import sys
import os
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_saga_compensation_flow():
    """测试 Saga 补偿操作流程"""
    
    print("🧪 测试 Saga 补偿操作流程:")
    print("=" * 60)
    
    # 模拟 Saga 事务管理器
    class MockSagaManager:
        def __init__(self):
            self.compensations = []
            self.executed_compensations = []
        
        def add_compensation_action(self, action):
            self.compensations.append(action)
            print(f"  📝 添加补偿操作: {action.__name__}")
        
        def execute_compensations(self):
            print("  🔄 执行补偿操作...")
            for compensation in reversed(self.compensations):
                try:
                    compensation()
                    self.executed_compensations.append(compensation.__name__)
                    print(f"    ✅ 补偿操作成功: {compensation.__name__}")
                except Exception as e:
                    print(f"    ❌ 补偿操作失败: {compensation.__name__}, 错误: {e}")
    
    # 模拟 RAG 客户端
    class MockRagClient:
        def __init__(self):
            self.created_kbs = []
            self.deleted_kbs = []
            self.updated_kbs = []
        
        def create_kb(self, name, description, ali_uid, wy_id):
            kb_id = f"kb_{len(self.created_kbs) + 1}"
            self.created_kbs.append({
                'kb_id': kb_id,
                'name': name,
                'description': description,
                'ali_uid': ali_uid,
                'wy_id': wy_id
            })
            print(f"    🔵 RAG 创建知识库: {kb_id}")
            
            response = Mock()
            response.body = Mock()
            response.body.data = Mock()
            response.body.data.kb_id = kb_id
            return response
        
        def delete_kb(self, kb_id):
            self.deleted_kbs.append(kb_id)
            print(f"    🔴 RAG 删除知识库: {kb_id}")
            
            response = Mock()
            response.body = Mock()
            return response
        
        def update_kb(self, kb_id, name, description):
            self.updated_kbs.append({
                'kb_id': kb_id,
                'name': name,
                'description': description
            })
            print(f"    🟡 RAG 更新知识库: {kb_id}")
            
            response = Mock()
            response.body = Mock()
            return response
    
    # 模拟数据库操作
    class MockDatabase:
        def __init__(self):
            self.records = []
            self.should_fail = False
        
        def create_knowledge_base(self, kb_id, name, owner_ali_uid, owner_wy_id, description):
            if self.should_fail:
                raise ValueError("数据库创建失败")
            
            self.records.append({
                'kb_id': kb_id,
                'name': name,
                'owner_ali_uid': owner_ali_uid,
                'owner_wy_id': owner_wy_id,
                'description': description
            })
            print(f"    💾 数据库创建记录: {kb_id}")
        
        def update_knowledge_base(self, kb_id, name, description, is_update_selective):
            if self.should_fail:
                raise ValueError("数据库更新失败")
            
            print(f"    💾 数据库更新记录: {kb_id}")
        
        def soft_delete_knowledge_base(self, kb_id):
            if self.should_fail:
                raise ValueError("数据库删除失败")
            
            print(f"    💾 数据库软删除记录: {kb_id}")
            return True
    
    # 测试正常流程
    print("\n📋 测试正常流程:")
    saga_manager = MockSagaManager()
    rag_client = MockRagClient()
    db = MockDatabase()
    
    # 模拟 create_knowledge_base 的正常流程
    print("  🔵 开始创建知识库...")
    
    # 步骤1: 调用 RAG 服务创建知识库
    response = rag_client.create_kb(
        name="测试知识库",
        description="测试描述",
        ali_uid="12345",
        wy_id="wy123"
    )
    kb_id = response.body.data.kb_id
    
    # 添加补偿操作
    def delete_rag_kb():
        rag_client.delete_kb(kb_id=kb_id)
    saga_manager.add_compensation_action(delete_rag_kb)
    
    # 步骤2: 在本地数据库创建记录
    db.create_knowledge_base(
        kb_id=kb_id,
        name="测试知识库",
        owner_ali_uid=12345,
        owner_wy_id="wy123",
        description="测试描述"
    )
    
    print("  ✅ 知识库创建成功!")
    
    # 测试异常流程 - 数据库操作失败
    print("\n📋 测试异常流程 - 数据库操作失败:")
    saga_manager = MockSagaManager()
    rag_client = MockRagClient()
    db = MockDatabase()
    db.should_fail = True
    
    print("  🔵 开始创建知识库...")
    
    # 步骤1: 调用 RAG 服务创建知识库
    response = rag_client.create_kb(
        name="测试知识库",
        description="测试描述",
        ali_uid="12345",
        wy_id="wy123"
    )
    kb_id = response.body.data.kb_id
    
    # 添加补偿操作
    def delete_rag_kb():
        rag_client.delete_kb(kb_id=kb_id)
    saga_manager.add_compensation_action(delete_rag_kb)
    
    # 步骤2: 在本地数据库创建记录（会失败）
    try:
        db.create_knowledge_base(
            kb_id=kb_id,
            name="测试知识库",
            owner_ali_uid=12345,
            owner_wy_id="wy123",
            description="测试描述"
        )
    except ValueError as e:
        print(f"  ❌ 数据库操作失败: {e}")
        # 执行补偿操作
        saga_manager.execute_compensations()
    
    print("\n📊 测试结果总结:")
    print(f"  RAG 创建的知识库数量: {len(rag_client.created_kbs)}")
    print(f"  RAG 删除的知识库数量: {len(rag_client.deleted_kbs)}")
    print(f"  数据库记录数量: {len(db.records)}")
    print(f"  执行的补偿操作: {saga_manager.executed_compensations}")
    
    print("\n" + "=" * 60)
    print("🎉 Saga 补偿操作测试完成!")

if __name__ == "__main__":
    test_saga_compensation_flow() 