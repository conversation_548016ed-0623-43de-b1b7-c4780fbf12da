#!/usr/bin/env python3
"""
Redis性能测试脚本
测试Redis的读写性能
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.infrastructure.redis import RedisClient


def test_write_performance(count=100):
    """测试写入性能"""
    logger.info(f"=== 测试写入性能 ({count}次操作) ===")
    
    try:
        redis_client = RedisClient()
        
        start_time = time.time()
        
        for i in range(count):
            redis_client.set(f"perf:write:{i}", f"value_{i}")
        
        end_time = time.time()
        duration = end_time - start_time
        ops_per_second = count / duration
        
        logger.info(f"写入 {count} 个键耗时: {duration:.3f}秒")
        logger.info(f"写入性能: {ops_per_second:.1f} ops/sec")
        
        return True
        
    except Exception as e:
        logger.error(f"写入性能测试异常: {e}")
        return False


def test_read_performance(count=100):
    """测试读取性能"""
    logger.info(f"=== 测试读取性能 ({count}次操作) ===")
    
    try:
        redis_client = RedisClient()
        
        start_time = time.time()
        
        for i in range(count):
            value = redis_client.get(f"perf:write:{i}")
            assert value == f"value_{i}", f"读取错误: {value}"
        
        end_time = time.time()
        duration = end_time - start_time
        ops_per_second = count / duration
        
        logger.info(f"读取 {count} 个键耗时: {duration:.3f}秒")
        logger.info(f"读取性能: {ops_per_second:.1f} ops/sec")
        
        return True
        
    except Exception as e:
        logger.error(f"读取性能测试异常: {e}")
        return False


def test_mixed_performance(count=50):
    """测试混合读写性能"""
    logger.info(f"=== 测试混合读写性能 ({count*2}次操作) ===")
    
    try:
        redis_client = RedisClient()
        
        start_time = time.time()
        
        for i in range(count):
            # 写入
            redis_client.set(f"perf:mixed:{i}", f"mixed_value_{i}")
            # 读取
            value = redis_client.get(f"perf:mixed:{i}")
            assert value == f"mixed_value_{i}", f"混合操作错误: {value}"
        
        end_time = time.time()
        duration = end_time - start_time
        ops_per_second = (count * 2) / duration
        
        logger.info(f"混合操作 {count*2} 次耗时: {duration:.3f}秒")
        logger.info(f"混合性能: {ops_per_second:.1f} ops/sec")
        
        return True
        
    except Exception as e:
        logger.error(f"混合性能测试异常: {e}")
        return False


def cleanup_test_data():
    """清理测试数据"""
    logger.info("=== 清理测试数据 ===")
    
    try:
        redis_client = RedisClient()
        
        # 清理写入测试数据
        write_keys = redis_client.keys("perf:write:*")
        if write_keys:
            redis_client.delete(*write_keys)
            logger.info(f"清理写入测试数据: {len(write_keys)} 个键")
        
        # 清理混合测试数据
        mixed_keys = redis_client.keys("perf:mixed:*")
        if mixed_keys:
            redis_client.delete(*mixed_keys)
            logger.info(f"清理混合测试数据: {len(mixed_keys)} 个键")
        
        return True
        
    except Exception as e:
        logger.error(f"清理测试数据异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始Redis性能测试...")
    
    # 测试参数
    test_count = 200  # 减少测试数量以加快速度
    
    success_count = 0
    total_tests = 3
    
    # 写入性能测试
    if test_write_performance(test_count):
        success_count += 1
        logger.success("✅ 写入性能测试通过")
    else:
        logger.error("❌ 写入性能测试失败")
    
    # 读取性能测试
    if test_read_performance(test_count):
        success_count += 1
        logger.success("✅ 读取性能测试通过")
    else:
        logger.error("❌ 读取性能测试失败")
    
    # 混合性能测试
    if test_mixed_performance(test_count // 2):
        success_count += 1
        logger.success("✅ 混合性能测试通过")
    else:
        logger.error("❌ 混合性能测试失败")
    
    # 清理测试数据
    cleanup_test_data()
    
    # 总结
    logger.info(f"性能测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有Redis性能测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个性能测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("性能测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"性能测试过程中发生异常: {e}")
        sys.exit(1)
