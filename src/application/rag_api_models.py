"""
API模型
请求和响应的数据模型定义
"""

from locale import strcoll
from domain.utils.time_utils import TimeUtils
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Dict, Any, List


# ==================== RAG操作相关模型 ====================


class KnowledgeBaseCreateRequest(BaseModel):
    """创建知识库信息"""

    name: str = Field(..., description="知识库名称")
    description: str = Field(..., description="知识库描述")


class KnowledgeBaseCreateResponse(BaseModel):
    """创建知识库信息"""

    kb_id: str = Field(..., description="知识库ID")


class KnowledgeBaseListResponse(BaseModel):
    """查询知识库列表信息"""

    kb_id: str = Field(..., description="知识库ID")
    name: str = Field(..., description="知识库名称")
    description: Optional[str] = Field(..., description="知识库描述")
    document_count: Optional[int] = Field(None, description="文档数量")
    session_count: Optional[int] = Field(None, description="会话数量")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")

    @classmethod
    def from_orm_model(cls, orm_obj):
        return cls(
            kb_id=orm_obj.kb_id,
            name=orm_obj.name,
            description=orm_obj.description,
            document_count=None,
            session_count=None,
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
        )


class KnowledgeBaseListPaginationResponse(BaseModel):
    """查询知识库列表信息"""

    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")
    data: list[KnowledgeBaseListResponse] = Field(..., description="知识库列表")


class KnowledgeBaseDetailResponse(BaseModel):
    """知识库详情信息"""

    kb_id: str = Field(..., description="知识库ID")
    name: str = Field(..., description="知识库名称")
    description: Optional[str] = Field(..., description="知识库描述")
    owner_ali_uid: int = Field(..., description="所有者阿里UID")
    owner_wy_id: str = Field(..., description="所有者无影ID")
    document_count: Optional[int] = Field(None, description="文档数量")
    session_count: Optional[int] = Field(None, description="会话数量")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")

    @classmethod
    def from_orm_model(cls, orm_obj):
        return cls(
            kb_id=orm_obj.kb_id,
            name=orm_obj.name,
            description=orm_obj.description,
            owner_ali_uid=orm_obj.owner_ali_uid,
            owner_wy_id=orm_obj.owner_wy_id,
            document_count=None,
            session_count=None,
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
        )


class KnowledgeBaseUpdateRequest(BaseModel):
    """更新知识库信息"""

    kb_id: str = Field(..., description="知识库ID")
    name: Optional[str] = Field(None, description="知识库名称")
    description: Optional[str] = Field(None, description="知识库描述")
    is_update_selective: Optional[bool] = Field(
        True,
        description="是否选择性更新（True表示None值不更新，False表示None值会置空字段）",
    )


class KnowledgeBaseDeleteRequest(BaseModel):
    """删除知识库信息"""

    kb_id: str = Field(..., description="知识库ID")


# ==================== 文档操作相关模型 ====================


class KnowledgeBaseDocumentItem(BaseModel):
    """文档项"""

    oss_path: str = Field(..., description="OSS路径")
    file_name: str = Field(..., description="文件名")


class KnowledgeBaseDocumentCreateRequest(BaseModel):
    """创建文档请求"""

    kb_id: str = Field(..., description="知识库ID")
    document_list: List[KnowledgeBaseDocumentItem] = Field(..., description="文档列表")


class KnowledgeBaseDocumentListResponse(BaseModel):
    """查询文档列表响应"""

    document_id: str = Field(..., description="文档ID")
    oss_path: str = Field(..., description="OSS路径")
    file_name: str = Field(..., description="文件名")
    state: str = Field(..., description="文档状态")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")

    @classmethod
    def from_orm_model(cls, orm_obj):
        return cls(
            document_id=orm_obj.document_id,
            oss_path=orm_obj.oss_path,
            file_name=orm_obj.file_name,
            state=orm_obj.state,
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
        )


class KnowledgeBaseDocumentListPaginationResponse(BaseModel):
    """查询文档列表信息"""

    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")
    data: list[KnowledgeBaseDocumentListResponse] = Field(..., description="文档列表")


class KnowledgeBaseDocumentDescribeRequest(BaseModel):
    """描述文档请求"""

    kb_id: str = Field(..., description="知识库ID")
    document_id: str = Field(..., description="文档ID")


class KnowledgeBaseDocumentDescribeResponse(BaseModel):
    """描述文档响应"""

    document_id: str = Field(..., description="文档ID")
    oss_path: str = Field(..., description="OSS路径")
    file_name: str = Field(..., description="文件名")
    state: str = Field(..., description="文档状态")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")


class KnowledgeBaseDocumentDeleteRequest(BaseModel):
    """删除文档请求"""

    kb_id: str = Field(..., description="知识库ID")
    document_id: str = Field(..., description="文档ID")


class KnowledgeBaseDocumentDeleteResponse(BaseModel):
    """删除文档响应"""


# ==================== 会话操作相关模型 ====================


class KnowledgeBaseSessionCreateRequest(BaseModel):
    """创建会话请求"""

    kb_id: str = Field(..., description="知识库ID")
    session_id: str = Field(..., description="会话ID")
    message_list: Optional[List[str]] = Field(None, description="消息列表")
    file_list: Optional[List[str]] = Field(None, description="附件列表")


class KnowledgeBaseSessionCreateResponse(BaseModel):
    """创建会话响应"""


class KnowledgeBaseSessionListRequest(BaseModel):
    """查询会话列表请求"""

    kb_id: str = Field(..., description="知识库ID")
    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")


class KnowledgeBaseSessionListResponse(BaseModel):
    """查询会话列表响应"""

    session_id: str = Field(..., description="会话ID")
    session_name: str = Field(None, description="会话名称")
    file_size: int = Field(0, description="文件大小")
    status: str = Field(None, description="会话状态")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")
    snippet_id: Optional[str] = Field(None, description="RAG映射ID")

    @classmethod
    def from_orm_model(cls, orm_obj):
        return cls(
            session_id=orm_obj.session_id,
            snippet_id=orm_obj.snippet_id,
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
            status="processing",
        )


class KnowledgeBaseSessionPaginationResponse(BaseModel):
    """查询会话列表响应"""

    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")
    data: list[KnowledgeBaseSessionListResponse] = Field(..., description="会话列表")


class KnowledgeBaseSessionMessageListRequest(BaseModel):
    """查询会话消息列表请求"""

    kb_id: str = Field(..., description="知识库ID")
    session_id: str = Field(..., description="会话ID")
    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")


class KnowledgeBaseSessionMessageListResponse(BaseModel):
    """查询会话消息列表响应"""

    message_id: str = Field(..., description="消息ID")
    role: str = Field(..., description="角色")
    content: str = Field(..., description="内容")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")


class KnowledgeBaseSessionMessagePaginationResponse(BaseModel):
    """查询会话消息列表响应"""

    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")
    data: list[KnowledgeBaseSessionMessageListResponse] = Field(
        ..., description="消息列表"
    )


class KnowledgeBaseSessionFileListRequest(BaseModel):
    """查询会话消息列表请求"""

    kb_id: str = Field(..., description="知识库ID")
    session_id: str = Field(..., description="会话ID")
    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")


class KnowledgeBaseSessionFileListResponse(BaseModel):
    """查询会话消息列表响应"""

    document_id: str = Field(..., description="文档ID")
    oss_path: str = Field(..., description="OSS路径")
    file_name: str = Field(..., description="文件名")
    status: str = Field(..., description="文档状态")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")

    @classmethod
    def from_orm_model(cls, orm_obj):
        return cls(
            document_id=orm_obj.doc_id,
            oss_path=orm_obj.oss_object_name,
            file_name=orm_obj.file_title,
            status=orm_obj.status,
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
        )


class KnowledgeBaseSessionFilePaginationResponse(BaseModel):
    """查询会话消息列表响应"""

    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")
    data: list[KnowledgeBaseSessionFileListResponse] = Field(
        ..., description="文件列表"
    )


class KnowledgeBaseSessionUpdateRequest(BaseModel):
    """更新会话请求"""

    kb_id: str = Field(..., description="知识库ID")
    session_id: str = Field(..., description="会话ID")
    message_list: Optional[List[str]] = Field(None, description="消息列表")
    file_list: Optional[List[str]] = Field(None, description="附件列表")


class KnowledgeBaseSessionDeleteRequest(BaseModel):
    """删除会话请求"""

    kb_id: str = Field(..., description="知识库ID")
    session_id: str = Field(..., description="会话ID")


class KnowledgeBaseSessionDeleteResponse(BaseModel):
    """删除会话响应"""


# ==================== 日志操作相关模型 ====================


class KnowledgeBaseLogListRequest(BaseModel):
    """查询日志列表请求"""

    kb_id: str = Field(..., description="知识库ID")
    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")


class KnowledgeBaseLogListResponse(BaseModel):
    """查询日志列表响应"""

    status: str = Field(..., description="状态")
    kb_id: str = Field(..., description="知识库ID")
    kb_name: Optional[str] = Field(None, description="知识库名称")
    title: Optional[str] = Field(None, description="标题")
    target_id: str = Field(..., description="目标ID")
    target_type: str = Field(..., description="目标类型")
    type: str = Field(..., description="类型")
    gmt_created: str = Field(..., description="创建时间")
    gmt_modified: str = Field(..., description="更新时间")

    @classmethod
    def from_orm_model(cls, orm_obj):
        return cls(
            kb_id=orm_obj.kb_id,
            target_id=orm_obj.target_id,
            target_type=orm_obj.target_type,
            status=orm_obj.status,
            type=orm_obj.operation_type,
            gmt_created=TimeUtils.to_iso8601_utc(orm_obj.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(orm_obj.gmt_modified),
        )


class KnowledgeBaseLogListPaginationResponse(BaseModel):
    """查询日志列表信息"""

    max_results: int = Field(..., description="最大结果数")
    next_token: Optional[str] = Field(None, description="下一页标记")
    data: list[KnowledgeBaseLogListResponse] = Field(..., description="日志列表")
