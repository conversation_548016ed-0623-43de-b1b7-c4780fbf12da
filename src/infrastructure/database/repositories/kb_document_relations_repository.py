"""
知识库文档关联数据库服务层
提供知识库文档关联的CRUD操作
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session as SQLSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_, UnaryExpression
from loguru import logger

from ..models.knowledgebase_models import KbDocumentRelationModel
from ..connection import db_manager


class KbDocumentRelationRepository:
    """知识库文档关联数据库仓库"""

    def __init__(self):
        self.db_manager = db_manager

    def create_relation(
        self, kb_id: str, file_id: str, session_id: Optional[str] = None
    ) -> dict:
        """创建知识库文档关联，返回 dict"""
        try:
            assert kb_id, "kb_id is required"
            assert file_id, "file_id is required"
            relation = KbDocumentRelationModel(
                kb_id=kb_id,
                file_id=file_id,
                session_id=session_id,
                is_deleted=0,
            )
            with self.db_manager.get_session() as db_session:
                db_session.add(relation)
                db_session.flush()
                logger.info(
                    f"[KbDocumentRelationDB] 创建关联: kb_id={kb_id}, file_id={file_id}"
                )
                return relation.to_dict()
        except IntegrityError as e:
            logger.error(
                f"[KbDocumentRelationDB] 关联已存在: kb_id={kb_id}, file_id={file_id}"
            )
            raise ValueError(f"关联已存在: kb_id={kb_id}, file_id={file_id}")
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 创建关联失败: {e}")
            raise

    def batch_create_relations(
        self, kb_id: str, file_ids: List[str], session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """批量创建知识库文档关联，返回 dict"""
        try:
            with self.db_manager.get_session() as db_session:
                relations = []
                for file_id in file_ids:
                    relation = KbDocumentRelationModel(
                        kb_id=kb_id,
                        file_id=file_id,
                        session_id=session_id,
                        is_deleted=0,
                    )
                    relations.append(relation)
                db_session.add_all(relations)
                db_session.flush()
                logger.info(
                    f"[KbDocumentRelationDB] 批量创建关联: kb_id={kb_id}, 文件数量={len(relations)}"
                )
                return [relation.to_dict() for relation in relations]
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 批量创建关联失败: {e}")
            raise

    def get_relation(
        self, kb_id: str, file_id: str
    ) -> Optional[KbDocumentRelationModel]:
        """根据kb_id和file_id获取关联"""
        try:
            with self.db_manager.get_session() as db_session:
                relation = (
                    db_session.query(KbDocumentRelationModel)
                    .filter(
                        and_(
                            KbDocumentRelationModel.kb_id == kb_id,
                            KbDocumentRelationModel.file_id == file_id,
                            KbDocumentRelationModel.is_deleted == 0,
                        )
                    )
                    .first()
                )

                if relation is not None:
                    db_session.refresh(relation)
                    db_session.expunge(relation)
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 获取关联失败: {e}")
            raise

    def list_relations(
        self,
        kb_id: str,
        session_id: Optional[str] = None,
        file_id: Optional[str] = None,
        file_id_list: Optional[List[str]] = None,
        limit: Optional[int] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        order_pairs: Optional[list[UnaryExpression]] = None,
    ) -> List[KbDocumentRelationModel]:
        """获取某知识库下所有文档关联"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentRelationModel).filter(
                    and_(
                        KbDocumentRelationModel.kb_id == kb_id,
                        KbDocumentRelationModel.is_deleted == 0,
                    )
                )
                if session_id:
                    query = query.filter(
                        KbDocumentRelationModel.session_id == session_id
                    )
                if less_than_equal_id:
                    query = query.filter(
                        KbDocumentRelationModel.id <= less_than_equal_id
                    )
                if less_than_equal_gmt_create:
                    query = query.filter(
                        KbDocumentRelationModel.gmt_created
                        <= less_than_equal_gmt_create
                    )
                if order_pairs:
                    query = query.order_by(*order_pairs)
                if limit:
                    query = query.limit(limit)
                results = query.all()

                # 确保所有延迟加载的属性都被加载
                for obj in results:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in results:
                    db_session.expunge(obj)
                    detached_results.append(obj)
                return detached_results

        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 获取知识库关联失败: {e}")
            raise

    def soft_delete_relation(self, kb_id: str, file_id: str) -> bool:
        """软删除知识库文档关联"""
        try:
            with self.db_manager.get_session() as db_session:
                relation = (
                    db_session.query(KbDocumentRelationModel)
                    .filter(
                        and_(
                            KbDocumentRelationModel.kb_id == kb_id,
                            KbDocumentRelationModel.file_id == file_id,
                            KbDocumentRelationModel.is_deleted == 0,
                        )
                    )
                    .first()
                )
                if not relation:
                    logger.warning(
                        f"[KbDocumentRelationDB] 关联不存在: kb_id={kb_id}, file_id={file_id}"
                    )
                    return False
                relation.is_deleted = 1
                relation.gmt_modified = datetime.now()
                logger.info(
                    f"[KbDocumentRelationDB] 软删除关联: kb_id={kb_id}, file_id={file_id}"
                )
                return True
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 软删除关联失败: {e}")
            raise

    def count_relations_by_kb(
        self, kb_id: str, session_id: Optional[str] = None
    ) -> int:
        """统计某知识库下的文档关联数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentRelationModel).filter(
                    and_(
                        KbDocumentRelationModel.kb_id == kb_id,
                        KbDocumentRelationModel.is_deleted == 0,
                    )
                )
                if session_id:
                    query = query.filter(
                        KbDocumentRelationModel.session_id == session_id
                    )
                return query.count()
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 统计关联数量失败: {e}")
            raise

    def count_relations_by_session(
        self, session_id: str, kb_id: Optional[str] = None
    ) -> int:
        """统计某会话下的文档关联数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentRelationModel).filter(
                    and_(
                        KbDocumentRelationModel.session_id == session_id,
                        KbDocumentRelationModel.is_deleted == 0,
                    )
                )
                if kb_id:
                    query = query.filter(KbDocumentRelationModel.kb_id == kb_id)
                return query.count()
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 统计会话关联数量失败: {e}")
            raise

    def batch_insert_document_relations(
        self, kb_id: str, file_ids: List[str], session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """批量插入知识库文档关联"""
        try:
            with self.db_manager.get_session() as db_session:
                relations = []
                for file_id in file_ids:
                    relation = KbDocumentRelationModel(
                        kb_id=kb_id,
                        file_id=file_id,
                        session_id=session_id,
                        is_deleted=0,
                    )
                    relations.append(relation)
                db_session.add_all(relations)
                db_session.flush()
                logger.info(
                    f"[KbDocumentRelationDB] 批量插入关联成功: kb_id={kb_id}, 文件数量={len(file_ids)}"
                )
                return [rel.to_dict() for rel in relations]
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 批量插入关联失败: {e}")
            raise


# 全局实例
kb_document_relations_repository = KbDocumentRelationRepository()
