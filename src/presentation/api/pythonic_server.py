"""
Pythonic服务器配置
使用简化的设计模式
"""

from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from loguru import logger


from .routes.pythonic_routes import router, global_router
from ..middleware.body_cache_middleware import BodyCacheMiddleware
from .routes.package_rlt_routes_sample import router as package_router
from ...infrastructure.oss.oss_service import oss_service
from ...shared.logging.logger import RequestContext
from ...presentation.api.dependencies.api_common_utils import get_request_id
from .routes.rag_routes import router as rag_router
from .routes.session_routes import router as session_router
from .routes.file_routes import router as file_router

class RequestIdMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 从请求头获取或生成request_id
        #req_id = request.headers.get('X-Request-ID') or str(uuid.uuid4())
        req_id = get_request_id(request)
        with RequestContext(req_id):
            response = await call_next(request)
            # 跳过健康检查接口的日志输出
            if not request.url.path.startswith('/status'):
                logger.info(f"Request completed: {request.url.path}")
            return response
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动
    logger.info("启动Pythonic Alpha Service...")

    # 初始化会话服务
    try:
        from ...domain.services.session_service import session_service
        await session_service.initialize()
        logger.info("会话服务初始化完成")
    except Exception as e:
        logger.error(f"会话服务初始化失败: {e}")
        raise

    try:
        yield

    finally:
        logger.info("🔄 关闭Pythonic Alpha Service...")

        # 关闭会话服务
        try:
            from ...domain.services.session_service import session_service
            await session_service.shutdown()
            logger.info("会话服务已关闭")
        except Exception as e:
            logger.error(f"会话服务关闭异常: {e}")

        logger.info("服务已关闭")

def create_pythonic_app() -> FastAPI:
    """创建Pythonic FastAPI应用"""

    app = FastAPI(
        title="Alpha Service - Pythonic Edition",
        description="Agent会话管理服务 - 简化版本",
        version="pythonic-v1.0",
        lifespan=lifespan
    )

    # 添加请求体缓存中间件（必须在其他中间件之前添加）
    app.add_middleware(BodyCacheMiddleware)
    app.add_middleware(RequestIdMiddleware)

    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册路由
    app.include_router(router)
    app.include_router(session_router)
    app.include_router(rag_router)  # 注册知识库相关路由
    #注册包装结果路由，后续可以去除掉
    app.include_router(package_router)
    app.include_router(global_router)  # 注册全局路由（包含/status.taobao）
    app.include_router(file_router)

    # 静态文件服务
    try:
        app.mount("/", StaticFiles(directory="static", html=True), name="static")
        logger.info("静态文件服务已启用")
    except Exception as e:
        logger.warning(f"静态文件服务启用失败: {e}")

    logger.info("Pythonic FastAPI应用创建完成")
    return app


# 创建应用实例
pythonic_app = create_pythonic_app()