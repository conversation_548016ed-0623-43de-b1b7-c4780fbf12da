"""
会话相关API路由
专门处理会话的发送消息、流式接口、历史查询、管理等功能
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
import uuid
from fastapi import APIRouter, HTTPException, Request, Query, UploadFile, File, Form, Depends
from fastapi.responses import StreamingResponse, PlainTextResponse
from loguru import logger
import httpx
import asyncio

from ....application.api_models import (
    SendMessageRequest, QueryHistoryResponse,
    SessionRenameRequest, SessionDeleteRequest,
    ResourceType, SessionResource,
    UpdateUserSettingRequest, UserSettingResponse
)
from ....domain.services.session_manager import session_manager
from ....popclients.waiy_infra_client import WaiyInfraClient, WaiyInfraClientError
from ....domain.services.auth_service import AuthContext, require_auth
from ....presentation.api.dependencies.common_params import CommonParams, UniversalCommonParams
from ....presentation.api.dependencies.api_common_utils import package_api_result, handle_exception, get_request_id

router = APIRouter(prefix="/api", tags=["session"])


@router.post("/sessions/send")
async def send_message(
    request: SendMessageRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id)
):
    """发送消息到Agent（接口1：发送消息接口）"""
    try:
        # 调用session_service处理消息发送
        from ....domain.services.session_service import session_service

        session_id, round_id = await session_service.send_message(
            session_id=request.session_id,
            prompt=request.prompt,
            agent_id=request.agent_id,
            desktop_id=request.desktop_id,
            auth_code=request.auth_code,
            resources=request.resources,
            context=context
        )

        logger.info(f"[API] 消息发送成功: session_id={session_id}, round_id={round_id}")
        return package_api_result(
            data={
                "session_id": session_id,
                "round_id": round_id
            },
            request_id=request_id
        )
        
    except Exception as e:
        logger.error(f"[API] 发送消息异常: {e}")
        return handle_exception(e, request_id)


@router.get("/sessions/stream")
async def stream_messages(
    session_id: str = Query(..., description="会话ID"),
    last_event_id: Optional[str] = Query(None, description="最后接收到的消息ID，用于断线重连")
):
    """建立SSE流连接"""
    #TODO 待重构
    try:
        logger.info(f"[API] 建立SSE流: session_id={session_id}")

        async def event_generator():
            try:
                # 使用session_service代替session_manager
                from ....domain.services.session_service import session_service
                import json
                import time

                # 消息去重缓存：记录已发送的历史消息ID，避免与实时消息重复
                sent_event_ids = set()
                history_send_time = None
                session_finished = False  # 标记会话是否已完成

                # 检查session创建时间是否小于3秒
                if session_service.is_session_created_recently(session_id, threshold_seconds=3000):
                    logger.info(f"[API] Session创建时间小于3秒，先发送历史消息: session_id={session_id}")
                    history_send_time = time.time()

                    # 获取历史消息的原始事件数据
                    events_result = session_service.get_raw_events(session_id, page_size=100)

                    if events_result and events_result.get("events"):
                        logger.info(f"[API] 开始发送{len(events_result['events'])}条历史消息")

                        # 历史事件转为正序（从旧到新）
                        events = list(reversed(events_result["events"]))

                        # 如果有last_event_id，过滤掉该ID之前的消息
                        if last_event_id:
                            logger.info(f"[API] 过滤历史消息，只发送last_event_id之后的消息: last_event_id={last_event_id}")
                            filtered_events = []
                            found_last_event = False

                            for event in events:
                                event_id = getattr(event, 'event_id', '')
                                if found_last_event:
                                    # 已经找到last_event_id，后续的消息都需要发送
                                    filtered_events.append(event)
                                elif event_id == last_event_id:
                                    # 找到了last_event_id，从下一个消息开始发送
                                    found_last_event = True
                                    logger.info(f"[API] 找到last_event_id位置: {last_event_id}")

                            if not found_last_event:
                                logger.warning(f"[API] 未找到last_event_id: {last_event_id}, 发送所有历史消息")
                                filtered_events = events
                            else:
                                logger.info(f"[API] 过滤后需要发送{len(filtered_events)}条历史消息")

                            events = filtered_events

                        # 检查历史消息中是否包含RUN_FINISHED事件
                        from memory.events import EventType
                        for event in events:
                            event_type = getattr(event, 'type', None)
                            if event_type:
                                # 安全获取事件类型值进行比较
                                run_finished_value = EventType.RUN_FINISHED.value if hasattr(EventType.RUN_FINISHED, 'value') else str(EventType.RUN_FINISHED)
                                run_error_value = EventType.RUN_ERROR.value if hasattr(EventType.RUN_ERROR, 'value') else str(EventType.RUN_ERROR)
                                current_type_value = event_type.value if hasattr(event_type, 'value') else str(event_type)

                                if current_type_value in [run_finished_value, run_error_value]:
                                    session_finished = True
                                    logger.info(f"[API] 历史消息中发现会话完成事件: session_id={session_id}, event_type={current_type_value}")
                                    break

                        # 发送历史消息
                        for event in events:
                            event_id = getattr(event, 'event_id', '')

                            # 记录已发送的历史消息ID
                            if event_id:
                                sent_event_ids.add(event_id)

                            # 构建与实时消息相同的事件格式
                            event_data = {
                                "event_id": event_id,
                                "type": getattr(event, 'type', ''),
                                "timestamp": getattr(event, 'timestamp', ''),
                                "session_id": getattr(event, 'session_id', ''),
                                "ext_data": getattr(event, 'ext_data', None),
                                "run_id": getattr(event, 'run_id', '')
                            }

                            # 添加可选字段
                            if hasattr(event, 'message_id') and getattr(event, 'message_id'):
                                event_data["message_id"] = getattr(event, 'message_id')
                            if hasattr(event, 'role') and getattr(event, 'role'):
                                event_data["role"] = getattr(event, 'role')
                            if hasattr(event, 'content') and getattr(event, 'content'):
                                event_data["content"] = getattr(event, 'content')
                            if hasattr(event, 'tool_call_id') and getattr(event, 'tool_call_id'):
                                event_data["tool_call_id"] = getattr(event, 'tool_call_id')
                            if hasattr(event, 'delta') and getattr(event, 'delta'):
                                event_data["delta"] = getattr(event, 'delta')
                            if hasattr(event, 'tool_name') and getattr(event, 'tool_name'):
                                event_data["tool_name"] = getattr(event, 'tool_name')

                            # 发送历史事件
                            yield f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"

                        logger.info(f"[API] 历史消息发送完成: session_id={session_id}, 缓存了{len(sent_event_ids)}个事件ID")

                # 如果历史消息中包含完成事件，直接关闭连接，不等待实时消息
                if session_finished:
                    logger.info(f"[API] 会话已完成，直接关闭SSE连接: session_id={session_id}")
                    return

                #创建SSE流连接用于接收新消息（这里会同时处理心跳和新消息）
                async for event in session_service.create_sse_stream(
                    session_id=session_id,
                    last_message_id=last_event_id
                ):
                    # 如果发送了历史消息，需要对实时消息进行去重处理
                    if sent_event_ids and history_send_time:
                        # 只在历史消息发送后的5分钟内进行去重，避免内存无限增长
                        if time.time() - history_send_time > 300:  # 5分钟
                            logger.info(f"[API] 历史消息去重缓存已过期，清空缓存: session_id={session_id}")
                            sent_event_ids.clear()
                            history_send_time = None
                        else:
                            # 尝试解析实时事件，检查是否重复
                            try:
                                # event是字符串格式，尝试解析
                                if event.startswith("data: "):
                                    event_json_str = event[6:].strip()  # 去掉"data: "前缀
                                    if event_json_str and event_json_str != "\n":
                                        event_data = json.loads(event_json_str)
                                        event_id = event_data.get("event_id")

                                        # 如果事件ID已经在历史消息中发送过，跳过此消息
                                        if event_id and event_id in sent_event_ids:
                                            logger.debug(f"[API] 跳过重复的实时消息: event_id={event_id}, session_id={session_id}")
                                            continue
                            except (json.JSONDecodeError, KeyError, AttributeError) as e:
                                # 解析失败时不影响消息发送，只记录调试日志
                                logger.debug(f"[API] 实时消息解析失败，直接发送: {e}")

                    # 格式化SSE事件
                    # event是一个字符串，不是字典
                    yield event
                        
            except Exception as e:
                logger.error(f"[API] SSE流异常: {e}")
                # 发送错误事件
                yield f"event: error\n"
                yield f'data: {{"error": "{str(e)}"}}\n\n'
        
        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except ValueError as e:
        logger.error(f"[API] SSE流创建失败: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"[API] SSE流异常: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/sessions/query")
async def query_session_history(
    session_id: str = Query(..., description="会话ID"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    next_token: Optional[str] = Query(None, description="下一页的令牌，用于分页"),
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id)
):
    """获取会话历史记录"""
    try:
        logger.info(f"[API] 获取会话历史: session_id={session_id}, page_size={page_size}, next_token={next_token}")

        # 调用session_service获取历史数据
        from ....domain.services.session_service import session_service
        
        # 使用鉴权后的用户信息，login_token 已经通过鉴权处理
        result = session_service.get_session_history(
            session_id=session_id,
            page_size=page_size,
            next_token=next_token
        )
        
        return package_api_result(
            code=200,
            success=True,
            next_token=result.get("nextToken"),
            data={"events": result.get('data', [])}
        )
    except Exception as e:
        logger.error(f"[API] 查询会话历史异常: {e}")
        return handle_exception(e, request_id)

@router.get("/sessions/list")
async def list_sessions(
    # 鉴权参数：通过依赖注入自动处理 loginToken、sessionId、regionId
    current_user: AuthContext = Depends(require_auth),
    page_size: int = Query(20, ge=1, le=500, description="每页数量"),
    next_token: Optional[str] = Query(None, description="下一页的令牌，用于分页"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
    agent_id: Optional[str] = Query(None, description="按Agent ID过滤"),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id)
):
    """
    获取用户会话列表

    通过鉴权参数自动识别用户身份，支持分页、排序、搜索和过滤功能
    """
    try:
        from ....domain.services.session_service import session_service, SessionListParams

        logger.info(f"[API] 获取会话列表: user={current_user.user_key}, page_size={page_size}, next_token={next_token}, search={search_keyword}")

        # 构建查询参数
        params = SessionListParams(
            page_size=page_size,
            next_token=next_token,
            search_keyword=search_keyword,
            agent_id=agent_id,
        )

        # 调用业务服务
        result = session_service.get_user_sessions(current_user, params)
        logger.info(f"[API] 业务服务调用成功: user={current_user.ali_uid}, 返回 {len(result.sessions)} 个会话")

        result_dict = result.to_dict()
        logger.info(f"[API] 结果序列化成功")

        return package_api_result(
            code=200,
            success=True,
            total_count=result.total_count,
            next_token=result.next_token,
            data=result_dict,
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 获取会话列表失败: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/sessions/rename")
async def rename_session(
    request: SessionRenameRequest,
    current_user: AuthContext = Depends(require_auth),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id)
):
    """
    重命名会话

    通过鉴权参数自动识别用户身份，只能重命名自己的会话
    """
    try:
        from ....domain.services.session_service import session_service

        logger.info(f"[API] 重命名会话: user={current_user.user_key}, session_id={request.session_id}, new_title={request.new_title}")

        # 调用业务服务
        success = session_service.rename_session(
            context=current_user,
            session_id=request.session_id,
            new_title=request.new_title
        )

        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或无权限修改")

        logger.info(f"[API] 会话重命名成功: user={current_user.user_key}, session_id={request.session_id}")

        return package_api_result(
            data={
                "session_id": request.session_id,
                "new_title": request.new_title
            },
            request_id=request_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 重命名会话异常: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/sessions/delete")
async def delete_session(
    request: SessionDeleteRequest,
    current_user: AuthContext = Depends(require_auth),
    common_params: CommonParams = UniversalCommonParams,
    request_id: str = Depends(get_request_id)
):
    """
    删除会话

    通过鉴权参数自动识别用户身份，只能删除自己的会话
    执行软删除，会话状态变为已删除但数据仍保留
    """
    try:
        from ....domain.services.session_service import session_service

        logger.info(f"[API] 删除会话: user={current_user.user_key}, session_id={request.session_id}")

        # 调用业务服务
        success = session_service.delete_session(
            context=current_user,
            session_id=request.session_id
        )

        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或无权限删除")

        logger.info(f"[API] 会话删除成功: user={current_user.user_key}, session_id={request.session_id}")

        return package_api_result(
            code=200,
            data={
                "session_id": request.session_id
            },
            request_id=request_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 删除会话异常: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.get("/sessions/stream/mock")
async def stream_mock(
    session_id: str = Query(..., description="会话ID"),
    last_event_id: Optional[str] = Query(None, description="最后接收到的消息ID，用于断线重连")
):
    """Mock SSE流连接，返回预定义的事件数据"""
    try:
        logger.info("[API] 建立Mock SSE流连接")
        
        # 预定义的事件数据 - 使用简化版本避免JSON转义问题
        mock_events = [
            '{"eventid":"event-1","type":"RUN_STARTED","timestamp":1753164703441,"session_id":"session-test","ext_data":null,"run_id":"trace_test"}',
            '{"eventid":"event-2","type":"TEXT_MESSAGE_START","timestamp":1753164709367,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_1","role":"user"}',
            '{"eventid":"event-3","type":"TEXT_MESSAGE_CONTENT","timestamp":1753164709367,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_1","content":"用户: 你好,帮我搜一下中东的新闻"}',
            '{"eventid":"event-4","type":"TEXT_MESSAGE_END","timestamp":1753164709367,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_1"}',
            '{"eventid":"event-5","type":"TEXT_MESSAGE_START","timestamp":1753164709439,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_2","role":"assistant"}',
            '{"eventid":"event-6","type":"TOOL_CALL_ARGS","timestamp":1753164709439,"session_id":"session-test","ext_data":null,"run_id":"run_test","tool_call_id":"call_search","delta":"{\\"input\\":\\"中东最近新闻\\"}"}',
            '{"eventid":"event-7","type":"TEXT_MESSAGE_END","timestamp":1753164709439,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_2"}',
            '{"eventid":"event-8","type":"TEXT_MESSAGE_START","timestamp":1753164724530,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_3","role":"assistant"}',
            '{"eventid":"event-9","type":"TOOL_CALL_ARGS","timestamp":1753164724530,"session_id":"session-test","ext_data":null,"run_id":"run_test","tool_call_id":"call_search2","delta":"{\\"category\\":\\"news_center\\",\\"query\\":\\"中东新闻\\",\\"time_range\\":\\"OneWeek\\"}"}',
            '{"eventid":"event-10","type":"TEXT_MESSAGE_END","timestamp":1753164724530,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_3"}',
            '{"eventid":"event-11","type":"TEXT_MESSAGE_START","timestamp":1753164744072,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_4","role":"assistant"}',
            '{"eventid":"event-12","type":"TEXT_MESSAGE_CONTENT","timestamp":1753164744072,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_4","content":"助手: 我找到了一些关于中东地区的最新新闻..."}',
            '{"eventid":"event-13","type":"TEXT_MESSAGE_END","timestamp":1753164744072,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_4"}',
            '{"eventid":"event-14","type":"TOOL_CALL_RESULT","timestamp":1753164776397,"session_id":"session-test","ext_data":null,"run_id":"run_test","message_id":"message_5","tool_call_id":"call_search","tool_name":"web_search","content":"搜索结果: 找到中东相关新闻5条","role":"tool"}',
            '{"eventid":"event-15","type":"RUN_FINISHED","timestamp":1753167921723,"session_id":"session-test","ext_data":null,"run_id":"trace_test"}'
        ]
        
        async def mock_event_generator():
            for i, event_data in enumerate(mock_events):
                # 发送事件数据
                yield f"data: {event_data}\n\n"
                logger.debug(f"[API] 发送SSE事件 {i+1}/{len(mock_events)}")
                
                # 每个事件间隔0.5秒，模拟实时流
                await asyncio.sleep(0.5)
                
            logger.info("[API] Mock SSE流发送完成")

        return StreamingResponse(
            mock_event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"[API] Mock SSE流异常: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@router.get("/environments/list")
async def list_user_environments(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id)
):
    """
    获取执行环境接口（接口14：获取执行环境接口）
    
    根据登录令牌获取用户的执行环境列表，包括云电脑和默认的AgentBAY环境
    """
    try:
        from ....domain.services.user_service import user_service

        logger.info(f"[API] 获取用户执行环境: user={current_user.user_key}, ali_uid={current_user.ali_uid}")

        # 调用业务服务
        result = user_service.get_user_environments(current_user)

        logger.info(f"[API] 执行环境获取成功: user={current_user.user_key}, 环境数量={result.total}")

        return package_api_result(
            data=result.to_dict(),
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 获取执行环境异常: user={current_user.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.post("/settings/update")
async def update_user_settings(
    request: UpdateUserSettingRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id)
):
    try:
        from ....domain.services.user_service import user_service

        logger.info(f"[API] 更新用户设置: user={context.user_key}, ali_uid={context.ali_uid}, "
                   f"desktop_id={request.desktop_id}, model={request.model}")

        # 调用业务服务更新用户设置
        setting_data = user_service.update_user_setting(
            context=context,
            desktop_id=request.desktop_id,
            model=request.model
        )

        logger.info(f"[API] 用户设置更新成功: user={context.user_key}")

        return package_api_result(
            code=200,
            data=setting_data.dict(),
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 更新用户设置异常: user={context.user_key}, error={e}")
        return handle_exception(e, request_id)


@router.get("/settings/get")
async def get_user_settings(
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id)
):
    """
    获取用户设置接口
    
    根据登录令牌识别用户身份，返回用户当前设置、可用环境列表、可用模型列表和版本信息
    """
    try:
        from ....domain.services.user_service import user_service

        logger.info(f"[API] 获取用户设置: user={context.user_key}, ali_uid={context.ali_uid}")

        # 调用业务服务获取用户设置
        setting_data = user_service.get_user_setting(context=context)

        logger.info(f"[API] 用户设置获取成功: user={context.user_key}")

        return package_api_result(
            data=setting_data.dict(),
            request_id=request_id
        )

    except Exception as e:
        logger.error(f"[API] 获取用户设置异常: user={context.user_key}, error={e}")
        return handle_exception(e, request_id)