# -*- coding: utf-8 -*-
"""
LoginVerifyClient - 封装阿里云无影AI内部服务客户端 (********版本)
提供简化的接口来访问无影AI内部登录验证服务
"""
from typing import Optional, Dict, Any
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_wuyingaiinner******** import client
from alibabacloud_wuyingaiinner********.models import (
    VerifyLoginTokenRequest,
    VerifyLoginTokenResponse
)
from loguru import logger

# 开发模式配置
try:
    from ...shared.config.dev_config import should_mock_login, get_test_user
except ImportError:
    # 如果导入失败，使用默认值
    def should_mock_login():
        return True
    def get_test_user():
        return {
            "ali_uid": ****************,
            "wy_id": "test_user",
            "end_user_id": "test_user",
            "account_type": "ALIYUN",
            "login_type": "PASSWORD",
            "api_key_id": "test_api_key",
            "extras": {"model": "test_model"}
        }


class LoginVerifyClient:
    """
    无影AI内部登录验证服务客户端封装类 (********版本)
    提供简化的接口来访问无影AI内部登录验证服务
    """
    
    def __init__(
        self,
        access_key_id: Optional[str] = None,
        access_key_secret: Optional[str] = None,
        endpoint: str = "wuyingaiinner-pre.aliyuncs.com",
        connect_timeout: int = 10000,
        read_timeout: int = 10000,
        **kwargs
    ):
        """
        初始化LoginVerifyClient
        
        Args:
            access_key_id: 阿里云AccessKey ID，如果为None则从配置中读取
            access_key_secret: 阿里云AccessKey Secret，如果为None则从配置中读取
            endpoint: 服务端点，默认为预发环境
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """
        # 如果没有提供access_key，则从配置中读取
        if access_key_id is None or access_key_secret is None:
            try:
                # 尝试多种导入方式
                try:
                    from src.shared.config.environments import env_manager
                except ImportError:
                    import sys
                    import os
                    # 添加项目根目录到Python路径
                    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                    if project_root not in sys.path:
                        sys.path.insert(0, project_root)
                    from src.shared.config.environments import env_manager
                
                # 从环境配置中获取阿里云凭证
                if access_key_id is None:
                    access_key_id = env_manager.get_config_value("ALIBABA_CLOUD_ACCESS_KEY_ID")
                if access_key_secret is None:
                    access_key_secret = env_manager.get_config_value("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
                    
                logger.info("已从环境配置中加载阿里云凭证")
            except Exception as e:
                logger.warning(f"无法从环境配置中加载阿里云凭证: {e}")
                if access_key_id is None or access_key_secret is None:
                    raise LoginVerifyClientError("必须提供access_key_id和access_key_secret，或确保环境配置可用")
        
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.endpoint = endpoint
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout
        
        # 创建配置
        self.config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            **kwargs
        )
        self.config.endpoint = endpoint
        
        # 初始化客户端
        self._client = client.Client(self.config)
    
    def verify_login_token(
        self,
        login_token,
        login_session_id: str,
        region_id: Optional[str] = None
    ) -> VerifyLoginTokenResponse:
        """
        验证登录令牌

        Args:
            login_token: 登录令牌
            login_session_id: 登录会话ID
            region_id: 区域ID，可选

        Returns:
            VerifyLoginTokenResponse: 验证结果响应
        """
        # 🔧 开发调试模式：检查是否应该模拟登录验证
        if should_mock_login():
            logger.info(f"[开发模式] 模拟验证登录令牌: login_token={login_token}, login_session_id={login_session_id}, region_id={region_id}")
            return self._create_mock_response(login_session_id or login_token)

        # 生产模式：执行真实的API调用
        try:
            request = VerifyLoginTokenRequest(
                login_token=login_token,
                session_id=login_session_id,
                region_id=region_id
            )

            logger.info(f"验证登录令牌: login_session_id={login_session_id}, region_id={region_id}")
            response = self._client.verify_login_token(request)

            if response.body and response.body.success:
                logger.info(f"登录令牌验证成功: login_session_id={login_session_id}")
            else:
                logger.warning(f"登录令牌验证失败: login_session_id={login_session_id}, message={response.body.message if response.body else 'Unknown'}")

            return response
        except Exception as e:
            logger.error(f"验证登录令牌失败: login_session_id={login_session_id}, error={str(e)}")
            raise LoginVerifyClientError(f"验证登录令牌失败: {str(e)}") from e
    
    async def verify_login_token_async(
        self,
        login_token: str,
        login_session_id: str,
        region_id: Optional[str] = None
    ) -> VerifyLoginTokenResponse:
        """
        异步验证登录令牌

        Args:
            login_session_id: 登录会话ID
            region_id: 区域ID，可选

        Returns:
            VerifyLoginTokenResponse: 验证结果响应
        """
        # 🔧 开发调试模式：检查是否应该模拟登录验证
        if should_mock_login():
            logger.info(f"[开发模式] 模拟异步验证登录令牌: login_session_id={login_session_id}, region_id={region_id}")
            return self._create_mock_response(f"async-{login_session_id}")

        # 生产模式：执行真实的API调用
        try:
            request = VerifyLoginTokenRequest(
                session_id=login_session_id,
                region_id=region_id,
                login_token=login_token
            )

            logger.info(f"异步验证登录令牌: login_session_id={login_session_id}, region_id={region_id}")
            response = await self._client.verify_login_token_async(request)

            if response.body and response.body.success:
                logger.info(f"异步登录令牌验证成功: login_session_id={login_session_id}")
            else:
                logger.warning(f"异步登录令牌验证失败: login_session_id={login_session_id}, message={response.body.message if response.body else 'Unknown'}")

            return response
        except Exception as e:
            logger.error(f"异步验证登录令牌失败: login_session_id={login_session_id}, error={str(e)}")
            raise LoginVerifyClientError(f"异步验证登录令牌失败: {str(e)}") from e
    
    def _create_mock_response(self, request_id: str) -> VerifyLoginTokenResponse:
        """
        创建模拟的成功响应

        Args:
            request_id: 请求ID

        Returns:
            VerifyLoginTokenResponse: 模拟的成功响应
        """
        from alibabacloud_wuyingaiinner********.models import (
            VerifyLoginTokenResponseBody,
            VerifyLoginTokenResponseBodyData
        )

        # 获取测试用户配置
        test_user = get_test_user()

        # 创建模拟的成功响应
        response = VerifyLoginTokenResponse()
        response.body = VerifyLoginTokenResponseBody()
        response.body.success = True
        response.body.code = "200"
        response.body.message = "Success"
        response.body.request_id = f"mock-request-{request_id}"

        # 创建模拟用户数据
        data = VerifyLoginTokenResponseBodyData()
        data.ali_uid = test_user["ali_uid"]
        data.wy_id = test_user["wy_id"]
        data.end_user_id = test_user["end_user_id"]
        data.account_type = test_user["account_type"]
        data.login_type = test_user["login_type"]
        data.api_key_id = test_user["api_key_id"]

        response.body.data = data

        logger.info(f"[开发模式] 创建模拟响应: ali_uid={test_user['ali_uid']}, wy_id={test_user['wy_id']}")
        return response

    def get_user_info_from_response(self, response: VerifyLoginTokenResponse) -> Optional[Dict[str, Any]]:
        """
        从验证响应中提取用户信息

        Args:
            response: 验证响应

        Returns:
            Dict[str, Any]: 用户信息字典，如果验证失败则返回None
        """
        if not response.body or not response.body.success or not response.body.data:
            return None

        data = response.body.data

        # 🔧 开发调试模式：检查是否应该返回固定测试用户
        if should_mock_login():
            test_user = get_test_user()
            logger.info(f"[LoginVerifyClient] 开发模式：返回固定测试用户 ali_uid={test_user['ali_uid']}, wy_id={test_user['wy_id']}")
            return test_user

        # 生产模式：从真实响应中提取用户信息
        user_info = {
            "ali_uid": data.ali_uid,
            "wy_id": data.wy_id,
            "end_user_id": data.end_user_id,
        }



        return user_info
    
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息
        
        Returns:
            Dict[str, Any]: 客户端配置信息
        """
        return {
            "access_key_id": self.access_key_id,
            "endpoint": self.endpoint,
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout,
            "version": "********"
        }
    
    def __str__(self) -> str:
        """返回客户端字符串表示"""
        return f"LoginVerifyClient(endpoint={self.endpoint}, access_key_id={self.access_key_id}, version=********)"
    
    def __repr__(self) -> str:
        """返回客户端详细字符串表示"""
        return self.__str__()


class LoginVerifyClientError(Exception):
    """LoginVerifyClient异常类"""
    pass
