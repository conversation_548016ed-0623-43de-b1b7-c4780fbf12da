#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 RAG 模型导入
"""

def test_rag_models_import():
    """测试 RAG 模型导入"""
    try:
        from alibabacloud_wuyingaiinner20250709 import models as rag_models
        print("✅ 成功导入 alibabacloud_wuyingaiinner20250709.models")
        
        # 检查 SubmitSnippetRagRequestMessages 是否存在
        if hasattr(rag_models, 'SubmitSnippetRagRequestMessages'):
            print("✅ SubmitSnippetRagRequestMessages 存在")
            print(f"   类型: {type(rag_models.SubmitSnippetRagRequestMessages)}")
            
            # 检查初始化参数
            import inspect
            sig = inspect.signature(rag_models.SubmitSnippetRagRequestMessages.__init__)
            print(f"   参数: {sig}")
        else:
            print("❌ SubmitSnippetRagRequestMessages 不存在")
            print(f"   可用属性: {[attr for attr in dir(rag_models) if 'Message' in attr]}")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_rag_client_import():
    """测试 RAG 客户端导入"""
    try:
        import sys
        import os
        
        # 添加项目路径
        current_dir = os.path.dirname(__file__)
        src_path = os.path.join(current_dir, 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
            
        from popclients.rag_client import RagClient
        print("✅ 成功导入 RagClient")
        
        # 尝试创建客户端实例
        client = RagClient()
        print("✅ 成功创建 RagClient 实例")
        
    except Exception as e:
        print(f"❌ RagClient 导入失败: {e}")

if __name__ == "__main__":
    print("=== 测试 RAG 模型导入 ===")
    test_rag_models_import()
    
    print("\n=== 测试 RAG 客户端导入 ===")
    test_rag_client_import() 