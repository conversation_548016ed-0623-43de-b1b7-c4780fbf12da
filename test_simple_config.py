#!/usr/bin/env python3
"""
简单配置测试脚本
直接测试 properties.toml 配置加载
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dynaconf import Dynaconf
from loguru import logger


def test_direct_config():
    """直接测试配置文件加载"""
    logger.info("=== 直接测试配置文件加载 ===")
    
    try:
        # 直接加载配置文件
        settings = Dynaconf(
            envvar_prefix="ALPHA",
            settings_files=['properties.toml'],
            environments=True,
            env_switcher="ENV_FOR_DYNACONF",
            load_dotenv=True,
            merge_enabled=True,
            default_env="daily"
        )
        
        logger.info(f"当前环境: {settings.current_env}")
        
        # 检查Redis配置
        redis_configs = {
            "redis_enabled": getattr(settings, 'redis_enabled', None),
            "redis_host": getattr(settings, 'redis_host', None),
            "redis_port": getattr(settings, 'redis_port', None),
            "redis_password": getattr(settings, 'redis_password', None),
            "redis_db": getattr(settings, 'redis_db', None),
        }
        
        logger.info("Redis配置项:")
        for key, value in redis_configs.items():
            display_value = "***" if key == "redis_password" and value else value
            logger.info(f"  {key}: {display_value}")
        
        # 检查其他已知配置
        logger.info("其他配置项:")
        logger.info(f"  api_host: {getattr(settings, 'api_host', None)}")
        logger.info(f"  api_port: {getattr(settings, 'api_port', None)}")
        logger.info(f"  db_mysql_host: {getattr(settings, 'db_mysql_host', None)}")
        
        return True
        
    except Exception as e:
        logger.error(f"配置测试异常: {e}")
        return False


def test_environment_switch():
    """测试环境切换"""
    logger.info("=== 测试环境切换 ===")
    
    try:
        # 设置环境变量
        os.environ["ENV_FOR_DYNACONF"] = "daily"
        
        settings = Dynaconf(
            envvar_prefix="ALPHA",
            settings_files=['properties.toml'],
            environments=True,
            env_switcher="ENV_FOR_DYNACONF",
            load_dotenv=True,
            merge_enabled=True,
        )
        
        logger.info(f"Daily环境 - redis_host: {getattr(settings, 'redis_host', None)}")
        logger.info(f"Daily环境 - redis_password: {'***' if getattr(settings, 'redis_password', None) else None}")
        
        # 切换到pre环境
        os.environ["ENV_FOR_DYNACONF"] = "pre"
        settings.reload()
        
        logger.info(f"Pre环境 - redis_host: {getattr(settings, 'redis_host', None)}")
        logger.info(f"Pre环境 - redis_db: {getattr(settings, 'redis_db', None)}")
        
        return True
        
    except Exception as e:
        logger.error(f"环境切换测试异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始简单配置测试...")
    
    # 测试直接配置加载
    if not test_direct_config():
        logger.error("直接配置测试失败")
        return False
    
    # 测试环境切换
    if not test_environment_switch():
        logger.error("环境切换测试失败")
        return False
    
    logger.success("简单配置测试完成！")
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
