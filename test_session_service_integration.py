#!/usr/bin/env python3
"""
会话服务集成测试脚本
直接测试SessionService的分页、重命名、删除功能
"""

import sys
import os
from unittest.mock import Mock, patch
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.domain.services.session_service import SessionService, SessionListParams
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.models.session_models import SessionModel


def create_mock_session_model(session_id: str, title: str, ali_uid: str = "123456", status: str = "ACTIVE"):
    """创建模拟的SessionModel"""
    mock_session = Mock(spec=SessionModel)
    mock_session.id = hash(session_id) % 1000000
    mock_session.session_id = session_id
    mock_session.title = title
    mock_session.ali_uid = ali_uid
    mock_session.agent_id = "test_agent"
    mock_session.wy_id = "test_user"
    mock_session.status = status
    mock_session.gmt_create = datetime.now()
    mock_session.gmt_modified = datetime.now()
    mock_session.meta_data = {}
    return mock_session


async def test_session_rename_integration():
    """测试会话重命名集成流程"""
    logger.info("=== 测试会话重命名集成流程 ===")
    
    try:
        # 创建测试数据
        mock_sessions = [
            create_mock_session_model("sess_001", "Python学习笔记"),
            create_mock_session_model("sess_002", "Java开发指南"),
            create_mock_session_model("sess_003", "前端技术分享"),
        ]
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        # 模拟数据库服务
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            # 1. 模拟获取会话列表
            mock_db_service.list_sessions.return_value = mock_sessions
            mock_db_service.count_sessions.return_value = len(mock_sessions)
            
            # 获取第一页会话
            params = SessionListParams(page_size=5)
            result = session_service.get_user_sessions(auth_context, params)
            
            logger.info(f"1. 获取到 {len(result.sessions)} 个会话")
            for i, session in enumerate(result.sessions):
                logger.info(f"   {i+1}. {session.session_id} - {session.title}")
            
            # 2. 选择第一个会话进行重命名
            target_session = result.sessions[0]
            session_id = target_session.session_id
            original_title = target_session.title
            new_title = f"重命名测试_{datetime.now().strftime('%H%M%S')}"
            
            logger.info(f"2. 重命名会话: {session_id}")
            logger.info(f"   原标题: {original_title}")
            logger.info(f"   新标题: {new_title}")
            
            # 模拟重命名操作和权限检查
            mock_db_service.get_session_by_id.return_value = mock_sessions[0]
            mock_db_service.update_session.return_value = True

            # 模拟权限检查通过
            with patch.object(session_service, 'get_session_with_permission_check') as mock_permission_check:
                mock_permission_check.return_value = mock_sessions[0]

                # 执行重命名
                rename_success = session_service.rename_session(
                    context=auth_context,
                    session_id=session_id,
                    new_title=new_title
                )
            
            if not rename_success:
                logger.error("重命名操作失败")
                return False
            
            logger.success("重命名操作成功")
            
            # 3. 验证重命名结果
            # 更新模拟数据以反映重命名
            mock_sessions[0].title = new_title
            
            # 再次获取会话列表
            updated_result = session_service.get_user_sessions(auth_context, params)
            
            # 查找重命名的会话
            renamed_session = None
            for session in updated_result.sessions:
                if session.session_id == session_id:
                    renamed_session = session
                    break
            
            if not renamed_session:
                logger.error(f"未找到重命名的会话: {session_id}")
                return False
            
            if renamed_session.title == new_title:
                logger.success(f"✅ 重命名验证成功: {renamed_session.title}")
                return True
            else:
                logger.error(f"❌ 重命名验证失败: 期望 '{new_title}', 实际 '{renamed_session.title}'")
                return False
                
    except Exception as e:
        logger.error(f"❌ 重命名集成测试异常: {e}")
        return False


async def test_session_delete_integration():
    """测试会话删除集成流程"""
    logger.info("=== 测试会话删除集成流程 ===")
    
    try:
        # 创建测试数据
        mock_sessions = [
            create_mock_session_model("sess_101", "待删除的会话1"),
            create_mock_session_model("sess_102", "待删除的会话2"),
            create_mock_session_model("sess_103", "保留的会话"),
        ]
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            # 1. 模拟获取会话列表
            mock_db_service.list_sessions.return_value = mock_sessions
            mock_db_service.count_sessions.return_value = len(mock_sessions)
            
            # 获取会话列表
            params = SessionListParams(page_size=5)
            result = session_service.get_user_sessions(auth_context, params)
            
            original_count = len(result.sessions)
            logger.info(f"1. 获取到 {original_count} 个会话")
            
            # 2. 选择最后一个会话进行删除
            target_session = result.sessions[-1]
            session_id = target_session.session_id
            session_title = target_session.title
            
            logger.info(f"2. 删除会话: {session_id}")
            logger.info(f"   标题: {session_title}")
            
            # 模拟删除操作和权限检查
            mock_db_service.get_session_by_id.return_value = mock_sessions[-1]
            mock_db_service.update_session.return_value = True

            # 模拟权限检查通过
            with patch.object(session_service, 'get_session_with_permission_check') as mock_permission_check:
                mock_permission_check.return_value = mock_sessions[-1]

                # 执行删除
                delete_success = session_service.delete_session(
                    context=auth_context,
                    session_id=session_id
                )
            
            if not delete_success:
                logger.error("删除操作失败")
                return False
            
            logger.success("删除操作成功")
            
            # 3. 验证删除结果
            # 更新模拟数据以反映删除（软删除，状态改为DELETE）
            mock_sessions[-1].status = "DELETE"
            
            # 模拟过滤掉已删除的会话
            active_sessions = [s for s in mock_sessions if s.status != "DELETE"]
            mock_db_service.list_sessions.return_value = active_sessions
            mock_db_service.count_sessions.return_value = len(active_sessions)
            
            # 再次获取会话列表
            updated_result = session_service.get_user_sessions(auth_context, params)
            updated_count = len(updated_result.sessions)
            
            # 检查会话是否被删除
            deleted_session_found = False
            for session in updated_result.sessions:
                if session.session_id == session_id:
                    deleted_session_found = True
                    break
            
            if not deleted_session_found and updated_count == original_count - 1:
                logger.success(f"✅ 删除验证成功: 会话已从列表中移除")
                logger.info(f"   删除前: {original_count} 个会话")
                logger.info(f"   删除后: {updated_count} 个会话")
                return True
            else:
                logger.error(f"❌ 删除验证失败: 会话仍在列表中或数量不正确")
                return False
                
    except Exception as e:
        logger.error(f"❌ 删除集成测试异常: {e}")
        return False


async def test_session_search_integration():
    """测试会话搜索集成流程"""
    logger.info("=== 测试会话搜索集成流程 ===")
    
    try:
        # 创建测试数据
        mock_sessions = [
            create_mock_session_model("sess_201", "Python基础教程"),
            create_mock_session_model("sess_202", "高级Python编程"),
            create_mock_session_model("sess_203", "Java开发指南"),
            create_mock_session_model("sess_204", "Python数据分析"),
            create_mock_session_model("sess_205", "前端开发技巧"),
        ]
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            # 1. 获取所有会话
            mock_db_service.list_sessions.return_value = mock_sessions
            mock_db_service.count_sessions.return_value = len(mock_sessions)
            
            params = SessionListParams(page_size=10)
            all_result = session_service.get_user_sessions(auth_context, params)
            
            logger.info(f"1. 总共 {len(all_result.sessions)} 个会话")
            
            # 2. 执行搜索
            search_keyword = "Python"
            logger.info(f"2. 搜索关键词: '{search_keyword}'")
            
            # 模拟搜索结果（包含Python的会话）
            python_sessions = [s for s in mock_sessions if "Python" in s.title]
            mock_db_service.list_sessions.return_value = python_sessions
            mock_db_service.count_sessions.return_value = len(python_sessions)
            
            search_params = SessionListParams(
                page_size=10,
                search_keyword=search_keyword
            )
            search_result = session_service.get_user_sessions(auth_context, search_params)
            
            logger.info(f"搜索结果: {len(search_result.sessions)} 个会话")
            
            # 3. 验证搜索结果
            valid_results = 0
            for session in search_result.sessions:
                if search_keyword in session.title:
                    valid_results += 1
                    logger.info(f"   ✅ {session.session_id} - {session.title}")
                else:
                    logger.warning(f"   ❓ {session.session_id} - {session.title}")
            
            expected_count = 3  # Python基础教程、高级Python编程、Python数据分析
            if len(search_result.sessions) == expected_count and valid_results == expected_count:
                logger.success(f"✅ 搜索验证成功: {valid_results} 个结果包含关键词")
                return True
            else:
                logger.error(f"❌ 搜索验证失败: 期望 {expected_count} 个结果，实际 {len(search_result.sessions)} 个")
                return False
                
    except Exception as e:
        logger.error(f"❌ 搜索集成测试异常: {e}")
        return False


async def test_session_pagination_integration():
    """测试会话分页集成流程"""
    logger.info("=== 测试会话分页集成流程 ===")
    
    try:
        # 创建大量测试数据
        mock_sessions = []
        for i in range(10):
            mock_sessions.append(
                create_mock_session_model(f"sess_{i:03d}", f"会话{i+1}")
            )
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            # 1. 获取第一页（page_size=3，实际查询4条用于判断是否有更多）
            first_page_sessions = mock_sessions[:4]  # 返回4条，表示还有更多
            mock_db_service.list_sessions.return_value = first_page_sessions
            mock_db_service.count_sessions.return_value = len(mock_sessions)
            
            params = SessionListParams(page_size=3)
            first_result = session_service.get_user_sessions(auth_context, params)
            
            logger.info(f"1. 第一页: {len(first_result.sessions)} 个会话")
            logger.info(f"   总数: {first_result.total_count}")
            logger.info(f"   有更多: {first_result.has_more}")
            logger.info(f"   Next Token: {first_result.next_token}")
            
            # 验证第一页结果
            if len(first_result.sessions) != 3:
                logger.error(f"第一页会话数量错误: 期望 3, 实际 {len(first_result.sessions)}")
                return False
            
            if not first_result.has_more:
                logger.error("第一页应该有更多数据")
                return False
            
            if not first_result.next_token:
                logger.error("第一页应该有next_token")
                return False
            
            # 2. 获取第二页
            second_page_sessions = mock_sessions[3:7]  # 第二页数据
            mock_db_service.list_sessions.return_value = second_page_sessions
            
            second_params = SessionListParams(
                page_size=3,
                next_token=first_result.next_token
            )
            second_result = session_service.get_user_sessions(auth_context, second_params)
            
            logger.info(f"2. 第二页: {len(second_result.sessions)} 个会话")
            
            # 3. 验证分页结果
            first_page_ids = {s.session_id for s in first_result.sessions}
            second_page_ids = {s.session_id for s in second_result.sessions}
            
            # 检查是否有重复的会话ID
            overlap = first_page_ids & second_page_ids
            if overlap:
                logger.error(f"❌ 分页验证失败: 发现重复的会话ID {overlap}")
                return False
            else:
                logger.success("✅ 分页验证成功: 两页之间没有重复的会话")
                return True
                
    except Exception as e:
        logger.error(f"❌ 分页集成测试异常: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始会话服务集成测试...")
    
    success_count = 0
    total_tests = 4
    
    # 测试重命名集成
    if await test_session_rename_integration():
        success_count += 1
        logger.success("✅ 重命名集成测试通过")
    else:
        logger.error("❌ 重命名集成测试失败")
    
    logger.info("")
    
    # 测试删除集成
    if await test_session_delete_integration():
        success_count += 1
        logger.success("✅ 删除集成测试通过")
    else:
        logger.error("❌ 删除集成测试失败")
    
    logger.info("")
    
    # 测试搜索集成
    if await test_session_search_integration():
        success_count += 1
        logger.success("✅ 搜索集成测试通过")
    else:
        logger.error("❌ 搜索集成测试失败")
    
    logger.info("")
    
    # 测试分页集成
    if await test_session_pagination_integration():
        success_count += 1
        logger.success("✅ 分页集成测试通过")
    else:
        logger.error("❌ 分页集成测试失败")
    
    # 总结
    logger.info("")
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有会话服务集成测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        import asyncio
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
