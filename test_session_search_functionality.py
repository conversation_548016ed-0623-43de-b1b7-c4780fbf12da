#!/usr/bin/env python3
"""
测试会话搜索功能
验证会话列表接口的搜索关键词功能
"""

import sys
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.domain.services.session_service import SessionService, SessionListParams
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.models.session_models import SessionModel


def create_mock_session_model(session_id: str, title: str, ali_uid: str = "123456"):
    """创建模拟的SessionModel"""
    mock_session = Mock(spec=SessionModel)
    mock_session.id = hash(session_id) % 1000000  # 简单的ID生成
    mock_session.session_id = session_id
    mock_session.title = title
    mock_session.ali_uid = ali_uid
    mock_session.agent_id = "test_agent"
    mock_session.wy_id = "test_user"
    mock_session.status = "active"
    mock_session.gmt_create = datetime.now()
    mock_session.gmt_modified = datetime.now()
    mock_session.meta_data = {}
    return mock_session


def test_search_functionality_basic():
    """测试基本搜索功能"""
    logger.info("=== 测试基本搜索功能 ===")
    
    try:
        # 创建测试数据
        mock_sessions = [
            create_mock_session_model("session_1", "Python编程学习"),
            create_mock_session_model("session_2", "Java开发指南"),
            create_mock_session_model("session_3", "Python数据分析"),
            create_mock_session_model("session_4", "前端开发技巧"),
        ]
        
        # 模拟搜索"Python"的结果
        python_sessions = [s for s in mock_sessions if "Python" in s.title]
        
        # 创建认证上下文
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        # 创建SessionService实例
        session_service = SessionService()
        
        # 模拟数据库服务
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            # 设置模拟返回值
            mock_db_service.list_sessions.return_value = python_sessions
            mock_db_service.count_sessions.return_value = len(python_sessions)
            
            # 创建搜索参数
            params = SessionListParams(
                page_size=10,
                search_keyword="Python"
            )
            
            # 调用搜索功能
            result = session_service.get_user_sessions(auth_context, params)
            
            # 验证数据库调用参数
            mock_db_service.list_sessions.assert_called_once_with(
                limit=11,  # page_size + 1
                ali_uid="123456",
                agent_id=None,
                next_token=None,
                search_keyword="Python"
            )
            
            mock_db_service.count_sessions.assert_called_once_with(
                ali_uid="123456",
                agent_id=None,
                search_keyword="Python"
            )
            
            # 验证结果
            assert len(result.sessions) == 2
            assert result.total_count == 2
            assert all("Python" in session.title for session in result.sessions)
            
            logger.success("✅ 基本搜索功能测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 基本搜索功能测试失败: {e}")
        return False


def test_search_functionality_empty_keyword():
    """测试空搜索关键词的处理"""
    logger.info("=== 测试空搜索关键词的处理 ===")
    
    try:
        # 创建测试数据
        mock_sessions = [
            create_mock_session_model("session_1", "会话1"),
            create_mock_session_model("session_2", "会话2"),
        ]
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        # 测试不同的空值情况
        empty_keywords = [None, "", "   "]
        
        for keyword in empty_keywords:
            with patch.object(session_service, 'session_db_service') as mock_db_service:
                mock_db_service.list_sessions.return_value = mock_sessions
                mock_db_service.count_sessions.return_value = len(mock_sessions)
                
                params = SessionListParams(
                    page_size=10,
                    search_keyword=keyword
                )
                
                result = session_service.get_user_sessions(auth_context, params)
                
                # 验证传递给数据库的参数
                mock_db_service.list_sessions.assert_called_once_with(
                    limit=11,
                    ali_uid="123456",
                    agent_id=None,
                    next_token=None,
                    search_keyword=keyword
                )
                
                # 验证返回所有会话
                assert len(result.sessions) == 2
                assert result.total_count == 2
                
                logger.info(f"空关键词 '{keyword}' 处理正确")
        
        logger.success("✅ 空搜索关键词处理测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 空搜索关键词处理测试失败: {e}")
        return False


def test_search_with_pagination():
    """测试搜索与分页的集成"""
    logger.info("=== 测试搜索与分页的集成 ===")
    
    try:
        # 创建大量测试数据
        mock_sessions = []
        for i in range(15):
            title = f"Python学习第{i+1}课" if i < 10 else f"Java开发第{i-9}课"
            mock_sessions.append(create_mock_session_model(f"session_{i}", title))
        
        # 模拟搜索"Python"的结果（前10个）
        python_sessions = [s for s in mock_sessions if "Python" in s.title]
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            # 模拟第一页返回6条（page_size=5，实际查询limit=6用于判断是否有更多）
            first_page_sessions = python_sessions[:6]  # 返回6条，表示还有更多
            mock_db_service.list_sessions.return_value = first_page_sessions
            mock_db_service.count_sessions.return_value = 10  # 总共10条Python相关会话
            
            params = SessionListParams(
                page_size=5,
                search_keyword="Python",
                next_token=None
            )
            
            result = session_service.get_user_sessions(auth_context, params)
            
            # 验证分页逻辑
            assert len(result.sessions) == 5  # 应该返回5条（去掉用于判断的第6条）
            assert result.total_count == 10
            assert result.has_more == True  # 应该有更多数据
            assert result.next_token is not None  # 应该有下一页token
            
            # 验证数据库调用
            mock_db_service.list_sessions.assert_called_once_with(
                limit=6,  # page_size + 1
                ali_uid="123456",
                agent_id=None,
                next_token=None,
                search_keyword="Python"
            )
            
            logger.success("✅ 搜索与分页集成测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 搜索与分页集成测试失败: {e}")
        return False


def test_search_with_agent_filter():
    """测试搜索与Agent过滤的组合"""
    logger.info("=== 测试搜索与Agent过滤的组合 ===")
    
    try:
        mock_sessions = [
            create_mock_session_model("session_1", "Python编程"),
        ]
        
        auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user",
            end_user_id="test_end_user"
        )
        
        session_service = SessionService()
        
        with patch.object(session_service, 'session_db_service') as mock_db_service:
            mock_db_service.list_sessions.return_value = mock_sessions
            mock_db_service.count_sessions.return_value = 1
            
            params = SessionListParams(
                page_size=10,
                search_keyword="Python",
                agent_id="specific_agent"
            )
            
            result = session_service.get_user_sessions(auth_context, params)
            
            # 验证同时传递了搜索关键词和Agent过滤
            mock_db_service.list_sessions.assert_called_once_with(
                limit=11,
                ali_uid="123456",
                agent_id="specific_agent",
                next_token=None,
                search_keyword="Python"
            )
            
            mock_db_service.count_sessions.assert_called_once_with(
                ali_uid="123456",
                agent_id="specific_agent",
                search_keyword="Python"
            )
            
            logger.success("✅ 搜索与Agent过滤组合测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 搜索与Agent过滤组合测试失败: {e}")
        return False


def test_database_like_query_simulation():
    """模拟测试数据库LIKE查询逻辑"""
    logger.info("=== 模拟测试数据库LIKE查询逻辑 ===")
    
    try:
        # 模拟数据库中的会话标题
        db_titles = [
            "Python基础教程",
            "高级Python编程",
            "Java开发指南",
            "python数据分析",  # 小写
            "学习Python的技巧",
            "前端开发",
            "Python Web开发"
        ]
        
        # 模拟LIKE查询逻辑
        def simulate_like_query(titles, keyword):
            if not keyword:
                return titles
            keyword = keyword.strip()
            if not keyword:
                return titles
            # 模拟SQL的LIKE '%keyword%'查询（不区分大小写）
            return [title for title in titles if keyword.lower() in title.lower()]
        
        # 测试不同的搜索关键词
        test_cases = [
            ("Python", 5),  # 应该匹配5个包含Python的标题
            ("python", 5),  # 小写也应该匹配5个
            ("Java", 1),    # 应该匹配1个
            ("开发", 3),    # 应该匹配3个包含"开发"的标题
            ("不存在", 0),  # 应该匹配0个
            ("", 7),       # 空字符串应该返回所有7个
            ("   ", 7),    # 空白字符串应该返回所有7个
        ]
        
        for keyword, expected_count in test_cases:
            result = simulate_like_query(db_titles, keyword)
            assert len(result) == expected_count, f"关键词'{keyword}'期望{expected_count}个结果，实际{len(result)}个"
            logger.info(f"关键词'{keyword}': {len(result)}个匹配结果 ✓")
        
        logger.success("✅ 数据库LIKE查询逻辑模拟测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库LIKE查询逻辑模拟测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始会话搜索功能测试...")
    
    success_count = 0
    total_tests = 5
    
    # 测试基本搜索功能
    if test_search_functionality_basic():
        success_count += 1
        logger.success("✅ 基本搜索功能测试通过")
    else:
        logger.error("❌ 基本搜索功能测试失败")
    
    # 测试空关键词处理
    if test_search_functionality_empty_keyword():
        success_count += 1
        logger.success("✅ 空关键词处理测试通过")
    else:
        logger.error("❌ 空关键词处理测试失败")
    
    # 测试搜索与分页集成
    if test_search_with_pagination():
        success_count += 1
        logger.success("✅ 搜索与分页集成测试通过")
    else:
        logger.error("❌ 搜索与分页集成测试失败")
    
    # 测试搜索与Agent过滤组合
    if test_search_with_agent_filter():
        success_count += 1
        logger.success("✅ 搜索与Agent过滤组合测试通过")
    else:
        logger.error("❌ 搜索与Agent过滤组合测试失败")
    
    # 测试数据库查询逻辑模拟
    if test_database_like_query_simulation():
        success_count += 1
        logger.success("✅ 数据库查询逻辑模拟测试通过")
    else:
        logger.error("❌ 数据库查询逻辑模拟测试失败")
    
    # 总结
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有会话搜索功能测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
