<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent ID 下拉框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
            color: #1f2937;
        }
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
        }
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        .test-button:hover {
            background: #357abd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Agent ID 下拉框测试</h1>
        <p>这是对修改后的 Agent ID 下拉框的测试页面</p>
        
        <div class="form-group">
            <label for="agentId">Agent ID</label>
            <select id="agentId">
                <option value="alpha">alpha</option>
                <option value="deeper">deeper</option>
                <option value="deep-research" selected>deep-research</option>
            </select>
        </div>
        
        <button class="test-button" onclick="testSelection()">测试选择</button>
        
        <div class="result" id="result" style="display: none;">
            <strong>当前选择的 Agent ID:</strong> <span id="selectedValue"></span>
        </div>
    </div>

    <script>
        function testSelection() {
            const select = document.getElementById('agentId');
            const selectedValue = select.value;
            const resultDiv = document.getElementById('result');
            const selectedValueSpan = document.getElementById('selectedValue');
            
            selectedValueSpan.textContent = selectedValue;
            resultDiv.style.display = 'block';
            
            console.log('选择的 Agent ID:', selectedValue);
        }
        
        // 监听选择变化
        document.getElementById('agentId').addEventListener('change', function() {
            console.log('Agent ID 已更改为:', this.value);
        });
        
        // 页面加载时显示默认选择
        window.addEventListener('load', function() {
            testSelection();
        });
    </script>
</body>
</html>
