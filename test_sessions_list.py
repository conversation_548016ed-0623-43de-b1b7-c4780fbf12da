#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 /sessions/list 分页接口的脚本
"""

import requests
import json
import sys
import os
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SessionsListTester:
    """会话列表接口测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id = "sess_457279f19de947d395d8eb2843f1c43c"
        
    def test_sessions_list(
        self, 
        page_size: int = 20,
        next_token: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        测试会话列表接口
        
        Args:
            page_size: 每页数量 (1-100)
            next_token: 下一页令牌
            session_id: 按会话ID过滤
        """
        url = f"{self.base_url}/api/sessions/list"
        
        # 构建查询参数
        params = {
            "page_size": page_size,
            # 使用开发模式的认证参数
            "loginToken": "test_token",
            "regionId": "cn-hangzhou"
        }
        
        if next_token:
            params["next_token"] = next_token
            
        if session_id:
            params["session_id"] = session_id
        
        print(f"🔍 测试会话列表接口...")
        print(f"   URL: {url}")
        print(f"   参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.get(url, params=params, timeout=30)
            
            print(f"\n📊 响应结果:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 解析响应数据
                if result.get("success") and "data" in result:
                    data = result["data"]
                    sessions = data.get("sessions", [])
                    
                    print(f"\n✅ 接口调用成功:")
                    print(f"   - 返回会话数量: {len(sessions)}")
                    print(f"   - 每页大小: {data.get('page_size', 'N/A')}")
                    print(f"   - 总数量: {data.get('total_count', 'N/A')}")
                    print(f"   - 是否有更多: {data.get('has_more', 'N/A')}")
                    print(f"   - 下一页令牌: {data.get('next_token', 'N/A')}")
                    
                    # 显示会话详情
                    if sessions:
                        print(f"\n📋 会话列表:")
                        for i, session in enumerate(sessions[:3]):  # 只显示前3个
                            print(f"   [{i+1}] 会话ID: {session.get('session_id', 'N/A')}")
                            print(f"       标题: {session.get('title', 'N/A')}")
                            print(f"       状态: {session.get('status', 'N/A')}")
                            print(f"       创建时间: {session.get('gmt_create', 'N/A')}")
                            print(f"       修改时间: {session.get('gmt_modified', 'N/A')}")
                        
                        if len(sessions) > 3:
                            print(f"   ... 还有 {len(sessions) - 3} 个会话")
                    
                    return result
                else:
                    print(f"❌ 接口返回失败: {result.get('message', '未知错误')}")
                    return result
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                try:
                    error_result = response.json()
                    print(f"   错误详情: {json.dumps(error_result, indent=2, ensure_ascii=False)}")
                except:
                    print(f"   错误内容: {response.text}")
                return {"error": f"HTTP {response.status_code}", "content": response.text}
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败，请确保服务已启动 (python start_service.py)")
            return {"error": "连接失败"}
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return {"error": "请求超时"}
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return {"error": str(e)}

    def test_pagination(self):
        """测试分页功能"""
        print("\n" + "="*60)
        print("🔄 测试分页功能")
        print("="*60)
        
        # 第一页
        print("\n1️⃣ 获取第一页数据...")
        result1 = self.test_sessions_list(page_size=5)
        
        if result1.get("success") and result1.get("data", {}).get("has_more"):
            next_token = result1["data"].get("next_token")
            if next_token:
                print("\n2️⃣ 获取第二页数据...")
                result2 = self.test_sessions_list(page_size=5, next_token=next_token)
                return result1, result2
        
        return result1, None

    def test_with_session_filter(self):
        """测试按会话ID过滤"""
        print("\n" + "="*60)
        print("🔍 测试按会话ID过滤")
        print("="*60)
        
        return self.test_sessions_list(
            page_size=10,
            session_id=self.session_id
        )

    def test_different_page_sizes(self):
        """测试不同的页面大小"""
        print("\n" + "="*60)
        print("📏 测试不同页面大小")
        print("="*60)
        
        results = {}
        for page_size in [1, 5, 10, 20, 50]:
            print(f"\n🔸 测试页面大小: {page_size}")
            result = self.test_sessions_list(page_size=page_size)
            results[page_size] = result
            
        return results

def main():
    """主函数"""
    print("🚀 开始测试 /sessions/list 分页接口")
    print("="*60)
    
    tester = SessionsListTester()
    
    # 基础测试
    print("\n📋 基础功能测试")
    basic_result = tester.test_sessions_list()
    
    # 分页测试
    page_results = tester.test_pagination()
    
    # 过滤测试
    filter_result = tester.test_with_session_filter()
    
    # 不同页面大小测试
    size_results = tester.test_different_page_sizes()
    
    print("\n" + "="*60)
    print("✅ 测试完成")
    print("="*60)
    
    return {
        "basic": basic_result,
        "pagination": page_results,
        "filter": filter_result,
        "sizes": size_results
    }

if __name__ == "__main__":
    # 激活虚拟环境提示
    if not os.environ.get('VIRTUAL_ENV'):
        print("⚠️  建议先激活虚拟环境:")
        print("   source venv/bin/activate")
        print()
    
    try:
        results = main()
        print(f"\n📊 测试结果摘要:")
        print(f"   - 基础测试: {'✅' if results['basic'].get('success') else '❌'}")
        print(f"   - 分页测试: {'✅' if results['pagination'][0].get('success') else '❌'}")
        print(f"   - 过滤测试: {'✅' if results['filter'].get('success') else '❌'}")
        print(f"   - 页面大小测试: {'✅' if any(r.get('success') for r in results['sizes'].values()) else '❌'}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
