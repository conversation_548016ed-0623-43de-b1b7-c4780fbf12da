#!/usr/bin/env python3
"""
Redis配置测试脚本
测试Redis配置是否正确加载
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.shared.config.environments import env_manager


def test_redis_config():
    """测试Redis配置"""
    logger.info("=== 测试Redis配置 ===")
    
    try:
        # 获取配置
        config = env_manager.get_config()
        
        # 检查Redis配置项
        redis_configs = {
            "redis_enabled": getattr(config, 'redis_enabled', None),
            "redis_host": getattr(config, 'redis_host', None),
            "redis_port": getattr(config, 'redis_port', None),
            "redis_username": getattr(config, 'redis_username', None),
            "redis_password": getattr(config, 'redis_password', None),
            "redis_db": getattr(config, 'redis_db', None),
            "redis_max_connections": getattr(config, 'redis_max_connections', None),
            "redis_socket_timeout": getattr(config, 'redis_socket_timeout', None),
            "redis_socket_connect_timeout": getattr(config, 'redis_socket_connect_timeout', None),
        }
        
        logger.info("Redis配置项:")
        for key, value in redis_configs.items():
            # 隐藏密码
            if key == "redis_password" and value:
                display_value = "***"
            elif key == "redis_username" and value:
                display_value = "***"  # 也隐藏用户名以保护隐私
            else:
                display_value = value
            logger.info(f"  {key}: {display_value}")
        
        # 检查必要配置项
        required_configs = ["redis_enabled", "redis_host", "redis_port"]
        missing_configs = []
        
        for config_key in required_configs:
            if redis_configs[config_key] is None:
                missing_configs.append(config_key)
        
        if missing_configs:
            logger.error(f"缺少必要的Redis配置项: {missing_configs}")
            return False
        
        logger.success("Redis配置检查通过！")
        return True
        
    except Exception as e:
        logger.error(f"Redis配置测试异常: {e}")
        return False


def test_environment_configs():
    """测试不同环境的Redis配置"""
    logger.info("=== 测试不同环境的Redis配置 ===")
    
    from src.shared.config.environments import Environment
    
    environments = [Environment.DAILY, Environment.PRE, Environment.PROD]
    
    for env in environments:
        logger.info(f"\n--- {env.value} 环境配置 ---")
        
        try:
            # 切换环境
            env_manager.switch_environment(env)
            config = env_manager.get_config()
            
            # 获取Redis配置
            redis_host = getattr(config, 'redis_host', 'localhost')
            redis_port = getattr(config, 'redis_port', 6379)
            redis_username = getattr(config, 'redis_username', '')
            redis_password = getattr(config, 'redis_password', '')
            redis_db = getattr(config, 'redis_db', 0)

            logger.info(f"  Host: {redis_host}")
            logger.info(f"  Port: {redis_port}")
            logger.info(f"  Username: {'***' if redis_username else '(无)'}")
            logger.info(f"  Password: {'***' if redis_password else '(无)'}")
            logger.info(f"  Database: {redis_db}")
            
        except Exception as e:
            logger.error(f"{env.value} 环境配置测试失败: {e}")
    
    # 切换回daily环境
    env_manager.switch_environment(Environment.DAILY)
    logger.info("\n已切换回 daily 环境")


def main():
    """主函数"""
    logger.info("开始Redis配置测试...")
    
    # 测试当前环境配置
    if not test_redis_config():
        logger.error("Redis配置测试失败")
        return False
    
    # 测试不同环境配置
    test_environment_configs()
    
    logger.success("Redis配置测试完成！")
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
