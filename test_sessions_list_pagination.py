#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话列表分页接口
使用指定的sessionId: sess_457279f19de947d395d8eb2843f1c43c
不包含search_keyword、agent_id参数
"""

import requests
import json
import sys
import os
import time
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SessionsListPaginationTester:
    """会话列表分页接口测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id = "sess_457279f19de947d395d8eb2843f1c43c"
        
    def test_sessions_list_pagination(
        self, 
        page_size: int = 20,
        next_token: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        测试会话列表分页接口
        
        Args:
            page_size: 每页数量 (1-100)
            next_token: 下一页令牌
            session_id: 按会话ID过滤
        """
        url = f"{self.base_url}/api/sessions/list"
        
        # 构建查询参数 - 不包含search_keyword和agent_id
        params = {
            "page_size": page_size,
            # 使用开发模式的认证参数
            "loginToken": "test_token",
            "regionId": "cn-hangzhou"
        }
        
        if next_token:
            params["next_token"] = next_token
            
        if session_id:
            params["session_id"] = session_id
        
        print(f"🔍 测试会话列表分页接口...")
        print(f"   URL: {url}")
        print(f"   参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.get(url, params=params, timeout=30)
            
            print(f"\n📊 响应结果:")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   响应体: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 解析响应数据
                if result.get("success") and "data" in result:
                    data = result["data"]
                    sessions = data.get("sessions", [])
                    
                    print(f"\n✅ 接口调用成功:")
                    print(f"   - 返回会话数量: {len(sessions)}")
                    print(f"   - 每页大小: {data.get('page_size', 'N/A')}")
                    print(f"   - 总数量: {data.get('total_count', 'N/A')}")
                    print(f"   - 是否有更多: {data.get('has_more', 'N/A')}")
                    print(f"   - 下一页令牌: {data.get('next_token', 'N/A')}")
                    
                    # 显示会话详情
                    if sessions:
                        print(f"\n📋 会话列表:")
                        for i, session in enumerate(sessions[:3]):  # 只显示前3个
                            print(f"   [{i+1}] 会话ID: {session.get('session_id', 'N/A')}")
                            print(f"       标题: {session.get('title', 'N/A')}")
                            print(f"       状态: {session.get('status', 'N/A')}")
                            print(f"       创建时间: {session.get('gmt_create', 'N/A')}")
                            print(f"       修改时间: {session.get('gmt_modified', 'N/A')}")
                        
                        if len(sessions) > 3:
                            print(f"   ... 还有 {len(sessions) - 3} 个会话")
                    
                    return result
                else:
                    print(f"❌ 响应格式异常: {result}")
                    return result
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                return {"error": f"HTTP {response.status_code}", "message": response.text}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return {"error": "NetworkError", "message": str(e)}
        except Exception as e:
            print(f"❌ 其他异常: {e}")
            return {"error": "UnknownError", "message": str(e)}
    
    def test_pagination_flow(self):
        """测试完整的分页流程"""
        print("🚀 开始测试会话列表分页流程")
        print("=" * 60)
        
        # 第一页
        print("\n📄 第1页 (page_size=10)")
        result1 = self.test_sessions_list_pagination(
            page_size=10,
            session_id=self.session_id
        )
        
        if not result1.get("success"):
            print("❌ 第一页请求失败，停止测试")
            return
        
        data1 = result1.get("data", {})
        next_token = data1.get("next_token")
        
        if not next_token:
            print("ℹ️ 没有下一页，测试结束")
            return
        
        # 等待一秒
        time.sleep(1)
        
        # 第二页
        print("\n📄 第2页 (使用next_token)")
        result2 = self.test_sessions_list_pagination(
            page_size=10,
            next_token=next_token,
            session_id=self.session_id
        )
        
        if not result2.get("success"):
            print("❌ 第二页请求失败")
            return
        
        data2 = result2.get("data", {})
        next_token2 = data2.get("next_token")
        
        if not next_token2:
            print("ℹ️ 没有更多页面")
        else:
            print(f"ℹ️ 还有更多页面，next_token: {next_token2[:20]}...")
        
        print("\n✅ 分页流程测试完成")

def main():
    """主函数"""
    print("🎯 会话列表分页接口测试")
    print("=" * 60)
    
    # 检查是否在venv环境中
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  警告: 建议在虚拟环境中运行此测试")
        print("   激活虚拟环境: source venv/bin/activate")
    
    # 创建测试器
    tester = SessionsListPaginationTester()
    
    # 测试基本功能
    print("\n🔧 测试基本功能...")
    result = tester.test_sessions_list_pagination(
        page_size=5,
        session_id=tester.session_id
    )
    
    if result.get("success"):
        print("\n✅ 基本功能测试通过")
        
        # 测试分页流程
        tester.test_pagination_flow()
    else:
        print("\n❌ 基本功能测试失败")
        print("请检查:")
        print("1. 服务是否正在运行 (http://localhost:8000)")
        print("2. 虚拟环境是否正确激活")
        print("3. 依赖是否正确安装")

if __name__ == "__main__":
    main() 