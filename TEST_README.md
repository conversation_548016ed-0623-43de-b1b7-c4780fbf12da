# FileService.get_download_urls 测试文档

本文档说明如何测试 `FileService.get_download_urls` 方法。

## 测试文件说明

### 1. `tests/test_file_service_download_urls.py`
完整的 pytest 单元测试文件，包含以下测试用例：

- **test_get_download_urls_success**: 测试成功获取下载链接
- **test_get_download_urls_file_not_found**: 测试文件不存在的情况
- **test_get_download_urls_file_not_completed**: 测试文件未上传完成的情况
- **test_get_download_urls_generate_url_failed**: 测试生成下载链接失败的情况
- **test_get_download_urls_mixed_results**: 测试混合结果（部分成功，部分失败）
- **test_get_download_urls_repository_exception**: 测试数据库异常的情况
- **test_get_download_urls_empty_file_ids**: 测试空文件ID列表

### 2. `test_download_urls_simple.py`
简单的直接调用测试脚本，用于：

- 直接调用真实的 `file_service.get_download_urls` 方法
- 测试指定的文件ID: `artifact-65aedb6d788442ee8d017bf39c7c2e9e`
- 测试不同的过期时间设置
- 测试多个文件ID（包含不存在的文件）

### 3. `run_download_urls_test.py`
测试运行器，提供统一的测试入口：

- 环境检查
- 选择性运行不同类型的测试
- 集成 pytest 和直接调用测试

## 运行测试

### 方法一：使用测试运行器（推荐）

```bash
python run_download_urls_test.py
```

然后根据提示选择测试方式：
1. 运行 pytest 单元测试（推荐）
2. 运行直接调用测试
3. 运行所有测试
4. 仅检查环境

### 方法二：直接运行 pytest

```bash
# 运行所有测试
python -m pytest tests/test_file_service_download_urls.py -v

# 运行特定测试
python -m pytest tests/test_file_service_download_urls.py::TestFileServiceDownloadUrls::test_get_download_urls_success -v

# 显示详细输出
python -m pytest tests/test_file_service_download_urls.py -v --tb=long
```

### 方法三：直接运行简单测试

```bash
python test_download_urls_simple.py
```

## 测试用例详解

### 成功场景测试
```python
# 测试成功获取下载链接
file_ids = ["artifact-65aedb6d788442ee8d017bf39c7c2e9e"]
result = file_service.get_download_urls(file_ids, expires=3600)

# 预期结果
assert len(result.download_links) == 1
assert len(result.failed_files) == 0
assert result.download_links[0].file_id == "artifact-65aedb6d788442ee8d017bf39c7c2e9e"
```

### 失败场景测试
```python
# 测试文件不存在
file_ids = ["nonexistent-file-id"]
result = file_service.get_download_urls(file_ids)

# 预期结果
assert len(result.download_links) == 0
assert len(result.failed_files) == 1
assert result.failed_files[0].error == "文件不存在"
```

### 混合场景测试
```python
# 测试部分成功，部分失败
file_ids = [
    "artifact-65aedb6d788442ee8d017bf39c7c2e9e",  # 存在的文件
    "nonexistent-file-id"                        # 不存在的文件
]
result = file_service.get_download_urls(file_ids)

# 预期结果：部分成功，部分失败
assert len(result.download_links) >= 0
assert len(result.failed_files) >= 0
```

## 测试数据

### 测试文件ID
- **主要测试ID**: `artifact-65aedb6d788442ee8d017bf39c7c2e9e`
- **不存在的ID**: `artifact-nonexistent-123`
- **其他测试ID**: `artifact-another-test`

### 测试参数
- **过期时间**: 300秒（5分钟）、1800秒（30分钟）、3600秒（1小时）、7200秒（2小时）
- **文件状态**: COMPLETED、UPLOADING、ANALYZING、FAILED

## 预期结果格式

### 成功响应
```json
{
  "download_links": [
    {
      "file_id": "artifact-65aedb6d788442ee8d017bf39c7c2e9e",
      "file_name": "example.txt",
      "file_size": 1024,
      "content_type": "text/plain",
      "download_url": "https://example.com/download/...",
      "expires_in": 3600
    }
  ],
  "failed_files": []
}
```

### 失败响应
```json
{
  "download_links": [],
  "failed_files": [
    {
      "file_id": "artifact-65aedb6d788442ee8d017bf39c7c2e9e",
      "file_name": "example.txt",
      "error": "文件不存在"
    }
  ]
}
```

## 注意事项

1. **环境要求**: 确保在项目根目录下运行测试
2. **数据库连接**: 直接调用测试需要真实的数据库连接
3. **文件存在性**: 测试文件ID `artifact-65aedb6d788442ee8d017bf39c7c2e9e` 需要在数据库中存在
4. **权限**: 确保有访问文件服务和OSS的权限
5. **网络**: 生成的下载链接需要网络访问OSS服务

## 故障排除

### 常见错误

1. **ImportError**: 检查 Python 路径和项目结构
2. **Database connection error**: 检查数据库配置和连接
3. **File not found**: 确认测试文件ID在数据库中存在
4. **OSS access error**: 检查OSS配置和权限

### 调试建议

1. 先运行环境检查: `python run_download_urls_test.py` 选择选项4
2. 查看详细错误信息: 使用 `--tb=long` 参数
3. 单独测试每个用例: 使用 pytest 的 `-k` 参数过滤测试
4. 检查日志输出: 确保日志级别设置正确

## 扩展测试

如需添加更多测试用例，可以：

1. 在 `TestFileServiceDownloadUrls` 类中添加新的测试方法
2. 修改 `test_download_urls_simple.py` 添加新的测试场景
3. 更新测试数据和预期结果

## 联系方式

如有问题，请联系开发团队或查看相关文档。
