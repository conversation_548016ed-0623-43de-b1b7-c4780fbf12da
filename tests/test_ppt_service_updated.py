#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT Service 更新后的测试用例

测试新的简化API实现，重点关注ali_uid参数版本的get_ppt_auth_code方法
"""

import pytest
from unittest.mock import Mock, patch

# 导入待测试的模块
try:
    from src.domain.services.ppt_service import PPTService, PPTServiceError, ppt_service
    from src.popclients.aippt_client import AIPPTClient, AIPPTClientError, AIPPTAuthCodeResponse
    from src.application.ppt_api_models import GetPPTAuthCodeResponse
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在正确的环境中运行测试")


class TestPPTServiceUpdated:
    """PPT Service 更新后的测试类"""
    
    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.ppt_service = PPTService()
    
    def test_ppt_service_initialization(self):
        """测试PPT服务初始化"""
        assert self.ppt_service is not None
        assert self.ppt_service.aippt_client is None  # 延迟初始化
        assert self.ppt_service.memory_sdk is None  # 延迟初始化
    
    @patch('src.popclients.aippt_client.get_aippt_client')
    def test_get_aippt_client(self, mock_get_client):
        """测试获取AIPPT客户端"""
        mock_client = Mock(spec=AIPPTClient)
        mock_get_client.return_value = mock_client
        
        client = self.ppt_service._get_aippt_client()
        
        assert client is mock_client
        assert self.ppt_service.aippt_client is mock_client
        mock_get_client.assert_called_once()
    
    @patch('src.domain.services.ppt_service.PPTService._get_aippt_client')
    def test_get_ppt_auth_code_success(self, mock_get_client):
        """测试获取PPT认证码成功"""
        # 准备模拟数据
        mock_client = Mock()
        mock_aippt_response = AIPPTAuthCodeResponse(
            code="test_auth_code_12345678",
            time_expire="86400"
        )
        mock_client.get_ppt_auth_code.return_value = mock_aippt_response
        mock_get_client.return_value = mock_client
        
        # 执行测试
        result = self.ppt_service.get_ppt_auth_code(ali_uid=123456)
        
        # 验证结果
        assert isinstance(result, GetPPTAuthCodeResponse)
        assert result.code == "test_auth_code_12345678"
        assert result.time_expire == "86400"  # 转换为字符串
        
        # 验证方法调用
        mock_client.get_ppt_auth_code.assert_called_once_with(ali_uid=123456)
    
    @patch('src.domain.services.ppt_service.PPTService._get_aippt_client')
    def test_get_ppt_auth_code_aippt_error(self, mock_get_client):
        """测试AIPPT客户端错误处理"""
        # 准备模拟错误
        mock_client = Mock()
        mock_client.get_ppt_auth_code.side_effect = AIPPTClientError("AIPPT服务不可用")
        mock_get_client.return_value = mock_client
        
        # 执行测试并验证异常
        with pytest.raises(AIPPTClientError) as exc_info:
            self.ppt_service.get_ppt_auth_code(ali_uid=123456)
        
        assert "AIPPT服务不可用" in str(exc_info.value)
    
    def test_save_ppt_not_implemented(self):
        """测试保存PPT功能（当前未实现）"""
        from src.domain.services.auth_service import AuthContext
        
        auth_context = AuthContext(ali_uid=123456, wy_id="test_user")
        
        with pytest.raises(NotImplementedError) as exc_info:
            self.ppt_service.save_ppt(
                context=auth_context,
                session_id="test_session",
                login_token="test_token",
                login_session_id="test_login_session",
                file_id="test_file_001"
            )
        
        assert "保存PPT功能待实现" in str(exc_info.value)


class TestPPTServiceGlobal:
    """测试全局PPT服务实例"""
    
    def test_global_ppt_service_instance(self):
        """测试全局PPT服务实例存在"""
        assert ppt_service is not None
        assert isinstance(ppt_service, PPTService)


@pytest.mark.parametrize("ali_uid,expected_code", [
    (123456, "code_123456"),
    (789012, "code_789012"),
    (999999, "code_999999"),
])
@patch('src.domain.services.ppt_service.PPTService._get_aippt_client')
def test_get_ppt_auth_code_parametrized(mock_get_client, ali_uid, expected_code):
    """参数化测试获取认证码"""
    # 准备模拟
    service = PPTService()
    
    mock_client = Mock()
    mock_aippt_response = AIPPTAuthCodeResponse(
        code=expected_code,
        time_expire="86400"
    )
    mock_client.get_ppt_auth_code.return_value = mock_aippt_response
    mock_get_client.return_value = mock_client
    
    # 执行测试
    result = service.get_ppt_auth_code(ali_uid=ali_uid)
    
    # 验证结果
    assert result.code == expected_code
    assert result.time_expire == "86400"
    mock_client.get_ppt_auth_code.assert_called_once_with(ali_uid=ali_uid)


if __name__ == "__main__":
    """
    运行测试的方法：
    
    1. 运行所有更新测试：
       pytest tests/test_ppt_service_updated.py -v
    
    2. 运行特定测试：
       pytest tests/test_ppt_service_updated.py::TestPPTServiceUpdated::test_get_ppt_auth_code_success -v
    
    3. 生成覆盖率报告：
       pytest tests/test_ppt_service_updated.py --cov=src.domain.services.ppt_service --cov-report=html
    """
    pytest.main([__file__, "-v"]) 