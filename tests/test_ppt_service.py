#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT Service 测试用例

测试PPT Service的各项功能，包括：
1. 获取PPT认证码
2. 保存PPT功能
3. 下载PPT功能
4. 获取封面图功能
5. 错误处理测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

# 导入待测试的模块
try:
    from src.domain.services.ppt_service import PPTService, PPTServiceError, ppt_service
    from src.domain.services.auth_service import AuthContext
    from src.popclients.aippt_client import AIPPTClient, AIPPTClientError, AIPPTAuthCodeResponse
except ImportError as e:
    # 在测试环境中可能需要调整导入路径
    print(f"导入错误: {e}")
    print("请确保在正确的环境中运行测试")


class TestPPTService:
    """PPT Service 测试类"""
    
    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.ppt_service = PPTService()
        self.auth_context = AuthContext(
            ali_uid=123456,
            wy_id="test_user_001"
        )
    
    def test_ppt_service_initialization(self):
        """测试PPT服务初始化"""
        assert self.ppt_service is not None
        assert self.ppt_service.aippt_client is None  # 延迟初始化
        assert self.ppt_service.memory_sdk is None  # 延迟初始化
    
    @patch('src.popclients.aippt_client.get_aippt_client')
    def test_get_aippt_client(self, mock_get_client):
        """测试获取AIPPT客户端"""
        mock_client = Mock(spec=AIPPTClient)
        mock_get_client.return_value = mock_client
        
        client = self.ppt_service._get_aippt_client()
        
        assert client is mock_client
        assert self.ppt_service.aippt_client is mock_client
        mock_get_client.assert_called_once()
    
    @patch('src.domain.services.ppt_service.MemorySDK')
    def test_get_memory_sdk(self, mock_memory_sdk_class):
        """测试获取MemorySDK"""
        mock_memory_sdk = Mock()
        mock_memory_sdk_class.return_value = mock_memory_sdk
        
        memory_sdk = self.ppt_service._get_memory_sdk()
        
        assert memory_sdk is mock_memory_sdk
        assert self.ppt_service.memory_sdk is mock_memory_sdk
        mock_memory_sdk_class.assert_called_once()
    
    @patch('src.domain.services.ppt_service.PPTService._get_aippt_client')
    def test_get_ppt_auth_code_success(self, mock_get_client):
        """测试获取PPT认证码成功"""
        # 准备模拟数据
        mock_client = Mock()
        mock_response = AIPPTAuthCodeResponse(
            code="test_auth_code_12345678",
            time_expire="86400"
        )
        mock_client.get_ppt_auth_code.return_value = mock_response
        mock_get_client.return_value = mock_client
        
        # 执行测试
        result = self.ppt_service.get_ppt_auth_code(ali_uid=123456)
        
        # 验证结果
        assert result.code == "test_auth_code_12345678"
        assert result.time_expire == "86400"
        
        # 验证方法调用
        mock_client.get_ppt_auth_code.assert_called_once_with(ali_uid=123456)
    
    @patch('src.domain.services.ppt_service.PPTService._get_aippt_client')
    def test_get_ppt_auth_code_aippt_error(self, mock_get_client):
        """测试AIPPT客户端错误处理"""
        # 准备模拟错误
        mock_client = Mock()
        mock_client.get_ppt_auth_code.side_effect = AIPPTClientError("AIPPT服务不可用")
        mock_get_client.return_value = mock_client
        
        # 执行测试并验证异常
        with pytest.raises(AIPPTClientError) as exc_info:
            self.ppt_service.get_ppt_auth_code(ali_uid=123456)
        
        assert "AIPPT服务不可用" in str(exc_info.value)
    
    @patch('src.domain.services.ppt_service.PPTService._get_aippt_client')
    def test_get_ppt_auth_code_unexpected_error(self, mock_get_client):
        """测试意外错误处理"""
        # 准备模拟错误
        mock_client = Mock()
        mock_client.get_ppt_auth_code.side_effect = Exception("网络错误")
        mock_get_client.return_value = mock_client
        
        # 执行测试并验证异常
        with pytest.raises(AIPPTClientError) as exc_info:
            self.ppt_service.get_ppt_auth_code(ali_uid=123456)
        
        assert "获取PPT认证code失败" in str(exc_info.value)
    
    def test_save_ppt_not_implemented(self):
        """测试保存PPT功能（当前未实现）"""
        with pytest.raises(NotImplementedError) as exc_info:
            self.ppt_service.save_ppt(
                context=self.auth_context,
                session_id="test_session",
                login_token="test_token",
                login_session_id="test_login_session",
                file_id="test_file_001"
            )
        
        assert "保存PPT功能待实现" in str(exc_info.value)
    
    def test_download_ppt_not_implemented(self):
        """测试下载PPT功能（当前未实现）"""
        with pytest.raises(NotImplementedError) as exc_info:
            self.ppt_service.download_ppt(
                context=self.auth_context,
                login_token="test_token",
                login_session_id="test_login_session",
                file_id="test_file_001"
            )
        
        assert "下载PPT功能待实现" in str(exc_info.value)
    
    def test_get_ppt_thumbnail_not_implemented(self):
        """测试获取PPT封面图功能（当前未实现）"""
        with pytest.raises(NotImplementedError) as exc_info:
            self.ppt_service.get_ppt_thumbnail(
                context=self.auth_context,
                login_token="test_token",
                login_session_id="test_login_session",
                file_id="test_file_001"
            )
        
        assert "获取PPT封面图功能待实现" in str(exc_info.value)
    
    @patch('src.domain.services.ppt_service.PPTService._get_memory_sdk')
    def test_save_to_artifact_repository(self, mock_get_memory_sdk):
        """测试保存到制品仓库（当前为日志输出）"""
        mock_memory_sdk = Mock()
        mock_get_memory_sdk.return_value = mock_memory_sdk
        
        # 测试私有方法（这里直接调用，实际项目中可能需要通过公共方法测试）
        self.ppt_service._save_to_artifact_repository(
            session_id="test_session",
            file_id="test_file_001",
            download_url="https://example.com/ppt/test_file_001.pptx",
            thumbnail_url="https://example.com/thumb/test_file_001.png"
        )
        
        # 验证MemorySDK被调用
        mock_get_memory_sdk.assert_called_once()


class TestPPTServiceGlobalInstance:
    """测试全局PPT服务实例"""
    
    def test_global_ppt_service_instance(self):
        """测试全局PPT服务实例存在"""
        assert ppt_service is not None
        assert isinstance(ppt_service, PPTService)


class TestPPTServiceError:
    """测试PPT服务异常类"""
    
    def test_ppt_service_error_creation(self):
        """测试PPT服务异常创建"""
        error_msg = "测试错误消息"
        error = PPTServiceError(error_msg)
        
        assert str(error) == error_msg
        assert isinstance(error, Exception)


class TestPPTServiceIntegration:
    """PPT Service 集成测试"""
    
    @pytest.mark.asyncio
    async def test_integration_with_mock_client(self):
        """使用模拟客户端的集成测试"""
        # 这个测试展示了如何进行更复杂的集成测试
        service = PPTService()
        auth_context = AuthContext(ali_uid=999, wy_id="integration_test")
        
        with patch.object(service, '_get_aippt_client') as mock_get_client:
            mock_client = Mock()
            mock_response = AIPPTAuthCodeResponse(
                code="integration_test_code",
                time_expire="2025-01-30T00:00:00"
            )
            mock_client.get_ppt_auth_code.return_value = mock_response
            mock_get_client.return_value = mock_client
            
            # 执行测试
            result = service.get_ppt_auth_code(ali_uid=999888)
            
            # 验证结果
            assert result.code == "integration_test_code"
            assert "86400" in result.time_expire


# 测试夹具和工具函数

@pytest.fixture
def auth_context():
    """认证上下文夹具"""
    return AuthContext(ali_uid=888, wy_id="fixture_user")


@pytest.fixture
def ppt_service_instance():
    """PPT服务实例夹具"""
    return PPTService()


def test_with_fixtures(auth_context, ppt_service_instance):
    """使用夹具的测试示例"""
    assert auth_context.ali_uid == 888
    assert auth_context.wy_id == "fixture_user"
    assert isinstance(ppt_service_instance, PPTService)


# 性能测试示例

@pytest.mark.performance
def test_ppt_service_initialization_performance():
    """测试PPT服务初始化性能"""
    import time
    
    start_time = time.time()
    service = PPTService()
    end_time = time.time()
    
    # PPT服务应该能够快速初始化（小于1秒）
    assert (end_time - start_time) < 1.0
    assert service is not None


if __name__ == "__main__":
    """
    运行测试的方法：
    
    1. 运行所有测试：
       pytest tests/test_ppt_service.py -v
    
    2. 运行特定测试：
       pytest tests/test_ppt_service.py::TestPPTService::test_get_ppt_auth_code_success -v
    
    3. 运行性能测试：
       pytest tests/test_ppt_service.py -m performance -v
    
    4. 生成覆盖率报告：
       pytest tests/test_ppt_service.py --cov=src.domain.services.ppt_service --cov-report=html
    
    注意：
    - 确保已安装pytest和相关依赖
    - 某些测试可能需要模拟环境变量或配置
    - 集成测试可能需要实际的服务环境
    """
    pytest.main([__file__, "-v"]) 