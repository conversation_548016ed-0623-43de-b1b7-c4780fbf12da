#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量查询会话功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.domain.services.session_service import SessionService
from src.infrastructure.database.repositories.session_repository import session_db_service

def test_batch_query_sessions():
    """测试批量查询会话功能"""
    print("=== 测试批量查询会话功能 ===")
    
    try:
        # 创建SessionService实例
        session_service = SessionService()
        
        # 1. 先查询一些现有的会话ID
        print("\n1. 查询现有会话...")
        existing_sessions = session_db_service.list_sessions(limit=5)
        if not existing_sessions:
            print("❌ 数据库中没有会话数据")
            return
        
        session_ids = [session.session_id for session in existing_sessions]
        print(f"✓ 找到 {len(session_ids)} 个会话:")
        for session in existing_sessions:
            print(f"  - {session.session_id}: {session.title}")
        
        # 2. 测试批量查询
        print(f"\n2. 批量查询会话: {session_ids}")
        batch_sessions = session_service.get_sessions_by_ids(session_ids)
        
        print(f"✓ 批量查询成功，返回 {len(batch_sessions)} 个会话:")
        for session_info in batch_sessions:
            print(f"  - {session_info.session_id}: {session_info.title}")
            print(f"    状态: {session_info.status}")
            print(f"    Agent: {session_info.agent_id}")
            print(f"    创建时间: {session_info.gmt_create}")
        
        # 3. 测试部分存在的会话ID
        print(f"\n3. 测试部分存在的会话ID...")
        mixed_ids = session_ids[:2] + ["sess_nonexistent1", "sess_nonexistent2"]
        mixed_sessions = session_service.get_sessions_by_ids(mixed_ids)
        
        print(f"✓ 混合查询成功，查询 {len(mixed_ids)} 个，返回 {len(mixed_sessions)} 个:")
        for session_info in mixed_sessions:
            print(f"  - {session_info.session_id}: {session_info.title}")
        
        # 4. 测试空列表
        print(f"\n4. 测试空列表...")
        empty_sessions = session_service.get_sessions_by_ids([])
        print(f"✓ 空列表查询成功，返回 {len(empty_sessions)} 个会话")
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_batch_query_sessions()
