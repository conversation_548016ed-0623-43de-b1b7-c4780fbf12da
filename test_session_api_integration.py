#!/usr/bin/env python3
"""
会话API集成测试脚本
测试分页获取、重命名、删除会话的完整流程
"""

import sys
import os
import json
import asyncio
import httpx
from typing import Optional, Dict, Any, List
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger


class SessionAPITester:
    """会话API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", login_token: str = None):
        """
        初始化测试器
        
        Args:
            base_url: API服务器地址
            login_token: 登录令牌
        """
        self.base_url = base_url.rstrip('/')
        self.login_token = login_token or "test_login_token_12345"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.login_token}",
            "loginToken": self.login_token,
            "sessionId": "test_session_id",
            "regionId": "cn-hangzhou"
        }
        
    async def get_sessions_list(
        self, 
        page_size: int = 10, 
        next_token: Optional[str] = None,
        search_keyword: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取会话列表"""
        url = f"{self.base_url}/api/v1/sessions/list"
        params = {"page_size": page_size}
        
        if next_token:
            params["next_token"] = next_token
        if search_keyword:
            params["search_keyword"] = search_keyword
        if agent_id:
            params["agent_id"] = agent_id
            
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
    
    async def rename_session(self, session_id: str, new_title: str) -> Dict[str, Any]:
        """重命名会话"""
        url = f"{self.base_url}/api/v1/sessions/rename"
        data = {
            "session_id": session_id,
            "new_title": new_title
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
    
    async def delete_session(self, session_id: str) -> Dict[str, Any]:
        """删除会话"""
        url = f"{self.base_url}/api/v1/sessions/delete"
        data = {
            "session_id": session_id
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()


async def test_session_rename_flow():
    """测试会话重命名流程"""
    logger.info("=== 测试会话重命名流程 ===")
    
    try:
        tester = SessionAPITester()
        
        # 1. 获取第一页会话列表
        logger.info("1. 获取会话列表...")
        sessions_response = await tester.get_sessions_list(page_size=5)
        
        if not sessions_response.get("success"):
            logger.error(f"获取会话列表失败: {sessions_response}")
            return False
            
        sessions_data = sessions_response.get("data", {})
        sessions = sessions_data.get("sessions", [])
        
        logger.info(f"获取到 {len(sessions)} 个会话")
        for i, session in enumerate(sessions):
            logger.info(f"  {i+1}. {session.get('session_id')} - {session.get('title')}")
        
        if not sessions:
            logger.warning("没有找到会话，无法进行重命名测试")
            return True
        
        # 2. 选择第一个会话进行重命名
        target_session = sessions[0]
        session_id = target_session.get("session_id")
        original_title = target_session.get("title")
        new_title = f"重命名测试_{datetime.now().strftime('%H%M%S')}"
        
        logger.info(f"2. 重命名会话: {session_id}")
        logger.info(f"   原标题: {original_title}")
        logger.info(f"   新标题: {new_title}")
        
        rename_response = await tester.rename_session(session_id, new_title)
        
        if not rename_response.get("success"):
            logger.error(f"重命名会话失败: {rename_response}")
            return False
            
        logger.success("重命名请求成功")
        
        # 3. 再次获取会话列表，验证重命名是否成功
        logger.info("3. 验证重命名结果...")
        await asyncio.sleep(1)  # 等待数据库更新
        
        updated_sessions_response = await tester.get_sessions_list(page_size=10)
        
        if not updated_sessions_response.get("success"):
            logger.error(f"获取更新后的会话列表失败: {updated_sessions_response}")
            return False
        
        updated_sessions = updated_sessions_response.get("data", {}).get("sessions", [])
        
        # 查找重命名的会话
        renamed_session = None
        for session in updated_sessions:
            if session.get("session_id") == session_id:
                renamed_session = session
                break
        
        if not renamed_session:
            logger.error(f"未找到重命名的会话: {session_id}")
            return False
        
        actual_title = renamed_session.get("title")
        if actual_title == new_title:
            logger.success(f"✅ 重命名验证成功: {actual_title}")
            return True
        else:
            logger.error(f"❌ 重命名验证失败: 期望 '{new_title}', 实际 '{actual_title}'")
            return False
            
    except Exception as e:
        logger.error(f"❌ 重命名流程测试异常: {e}")
        return False


async def test_session_delete_flow():
    """测试会话删除流程"""
    logger.info("=== 测试会话删除流程 ===")
    
    try:
        tester = SessionAPITester()
        
        # 1. 获取第一页会话列表
        logger.info("1. 获取会话列表...")
        sessions_response = await tester.get_sessions_list(page_size=5)
        
        if not sessions_response.get("success"):
            logger.error(f"获取会话列表失败: {sessions_response}")
            return False
            
        sessions_data = sessions_response.get("data", {})
        sessions = sessions_data.get("sessions", [])
        original_count = len(sessions)
        
        logger.info(f"获取到 {original_count} 个会话")
        
        if not sessions:
            logger.warning("没有找到会话，无法进行删除测试")
            return True
        
        # 2. 选择最后一个会话进行删除
        target_session = sessions[-1]
        session_id = target_session.get("session_id")
        session_title = target_session.get("title")
        
        logger.info(f"2. 删除会话: {session_id}")
        logger.info(f"   标题: {session_title}")
        
        delete_response = await tester.delete_session(session_id)
        
        if not delete_response.get("success"):
            logger.error(f"删除会话失败: {delete_response}")
            return False
            
        logger.success("删除请求成功")
        
        # 3. 再次获取会话列表，验证删除是否成功
        logger.info("3. 验证删除结果...")
        await asyncio.sleep(1)  # 等待数据库更新
        
        updated_sessions_response = await tester.get_sessions_list(page_size=10)
        
        if not updated_sessions_response.get("success"):
            logger.error(f"获取更新后的会话列表失败: {updated_sessions_response}")
            return False
        
        updated_sessions = updated_sessions_response.get("data", {}).get("sessions", [])
        updated_count = len(updated_sessions)
        
        # 检查会话是否被删除（软删除，不应该在列表中出现）
        deleted_session_found = False
        for session in updated_sessions:
            if session.get("session_id") == session_id:
                deleted_session_found = True
                break
        
        if not deleted_session_found:
            logger.success(f"✅ 删除验证成功: 会话已从列表中移除")
            logger.info(f"   删除前: {original_count} 个会话")
            logger.info(f"   删除后: {updated_count} 个会话")
            return True
        else:
            logger.error(f"❌ 删除验证失败: 会话仍在列表中")
            return False
            
    except Exception as e:
        logger.error(f"❌ 删除流程测试异常: {e}")
        return False


async def test_session_search_flow():
    """测试会话搜索流程"""
    logger.info("=== 测试会话搜索流程 ===")
    
    try:
        tester = SessionAPITester()
        
        # 1. 获取所有会话
        logger.info("1. 获取所有会话...")
        all_sessions_response = await tester.get_sessions_list(page_size=20)
        
        if not all_sessions_response.get("success"):
            logger.error(f"获取会话列表失败: {all_sessions_response}")
            return False
            
        all_sessions = all_sessions_response.get("data", {}).get("sessions", [])
        logger.info(f"总共 {len(all_sessions)} 个会话")
        
        if not all_sessions:
            logger.warning("没有找到会话，无法进行搜索测试")
            return True
        
        # 2. 选择一个会话的标题关键词进行搜索
        target_session = all_sessions[0]
        session_title = target_session.get("title", "")
        
        # 提取标题中的关键词（取前几个字符）
        search_keyword = session_title[:3] if len(session_title) >= 3 else session_title
        
        if not search_keyword:
            logger.warning("会话标题为空，使用默认搜索关键词")
            search_keyword = "会话"
        
        logger.info(f"2. 搜索关键词: '{search_keyword}'")
        
        # 3. 执行搜索
        search_response = await tester.get_sessions_list(
            page_size=10, 
            search_keyword=search_keyword
        )
        
        if not search_response.get("success"):
            logger.error(f"搜索会话失败: {search_response}")
            return False
            
        search_sessions = search_response.get("data", {}).get("sessions", [])
        logger.info(f"搜索结果: {len(search_sessions)} 个会话")
        
        # 4. 验证搜索结果
        valid_results = 0
        for session in search_sessions:
            title = session.get("title", "")
            if search_keyword.lower() in title.lower():
                valid_results += 1
                logger.info(f"   ✅ {session.get('session_id')} - {title}")
            else:
                logger.warning(f"   ❓ {session.get('session_id')} - {title} (不包含关键词)")
        
        if valid_results > 0:
            logger.success(f"✅ 搜索验证成功: {valid_results}/{len(search_sessions)} 个结果包含关键词")
            return True
        else:
            logger.warning("⚠️ 搜索结果中没有包含关键词的会话（可能是模糊匹配）")
            return True
            
    except Exception as e:
        logger.error(f"❌ 搜索流程测试异常: {e}")
        return False


async def test_session_pagination_flow():
    """测试会话分页流程"""
    logger.info("=== 测试会话分页流程 ===")
    
    try:
        tester = SessionAPITester()
        
        # 1. 获取第一页
        logger.info("1. 获取第一页...")
        first_page_response = await tester.get_sessions_list(page_size=3)
        
        if not first_page_response.get("success"):
            logger.error(f"获取第一页失败: {first_page_response}")
            return False
            
        first_page_data = first_page_response.get("data", {})
        first_page_sessions = first_page_data.get("sessions", [])
        next_token = first_page_response.get("next_token")
        total_count = first_page_response.get("total_count", 0)
        
        logger.info(f"第一页: {len(first_page_sessions)} 个会话")
        logger.info(f"总数: {total_count}")
        logger.info(f"Next Token: {next_token}")
        
        if not next_token:
            logger.info("没有下一页，分页测试完成")
            return True
        
        # 2. 获取第二页
        logger.info("2. 获取第二页...")
        second_page_response = await tester.get_sessions_list(
            page_size=3, 
            next_token=next_token
        )
        
        if not second_page_response.get("success"):
            logger.error(f"获取第二页失败: {second_page_response}")
            return False
            
        second_page_data = second_page_response.get("data", {})
        second_page_sessions = second_page_data.get("sessions", [])
        
        logger.info(f"第二页: {len(second_page_sessions)} 个会话")
        
        # 3. 验证分页结果
        first_page_ids = {s.get("session_id") for s in first_page_sessions}
        second_page_ids = {s.get("session_id") for s in second_page_sessions}
        
        # 检查是否有重复的会话ID
        overlap = first_page_ids & second_page_ids
        if overlap:
            logger.error(f"❌ 分页验证失败: 发现重复的会话ID {overlap}")
            return False
        else:
            logger.success("✅ 分页验证成功: 两页之间没有重复的会话")
            return True
            
    except Exception as e:
        logger.error(f"❌ 分页流程测试异常: {e}")
        return False


async def main():
    """主函数"""
    logger.info("开始会话API集成测试...")
    
    success_count = 0
    total_tests = 4
    
    # 测试重命名流程
    if await test_session_rename_flow():
        success_count += 1
        logger.success("✅ 重命名流程测试通过")
    else:
        logger.error("❌ 重命名流程测试失败")
    
    logger.info("")
    
    # 测试删除流程
    if await test_session_delete_flow():
        success_count += 1
        logger.success("✅ 删除流程测试通过")
    else:
        logger.error("❌ 删除流程测试失败")
    
    logger.info("")
    
    # 测试搜索流程
    if await test_session_search_flow():
        success_count += 1
        logger.success("✅ 搜索流程测试通过")
    else:
        logger.error("❌ 搜索流程测试失败")
    
    logger.info("")
    
    # 测试分页流程
    if await test_session_pagination_flow():
        success_count += 1
        logger.success("✅ 分页流程测试通过")
    else:
        logger.error("❌ 分页流程测试失败")
    
    # 总结
    logger.info("")
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有会话API集成测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
