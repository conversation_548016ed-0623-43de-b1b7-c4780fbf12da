#!/usr/bin/env python3
"""
Redis配置调试脚本
检查Redis配置是否正确加载
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.shared.config.environments import env_manager


def debug_redis_config():
    """调试Redis配置"""
    logger.info("=== Redis配置调试 ===")
    
    try:
        # 获取配置
        config = env_manager.get_config()
        
        # 显示当前环境
        logger.info(f"当前环境: {env_manager.current_env.value}")
        
        # 显示Redis配置
        logger.info("Redis配置详情:")
        logger.info(f"  redis_enabled: {getattr(config, 'redis_enabled', 'NOT_SET')}")
        logger.info(f"  redis_host: {getattr(config, 'redis_host', 'NOT_SET')}")
        logger.info(f"  redis_port: {getattr(config, 'redis_port', 'NOT_SET')}")
        logger.info(f"  redis_username: {getattr(config, 'redis_username', 'NOT_SET')}")
        
        # 显示密码信息（隐藏实际内容）
        password = getattr(config, 'redis_password', 'NOT_SET')
        if password and password != 'NOT_SET':
            if ':' in password:
                parts = password.split(':', 1)
                logger.info(f"  redis_password: {parts[0]}:*** (账号:密码格式)")
            else:
                logger.info(f"  redis_password: *** (纯密码格式)")
        else:
            logger.info(f"  redis_password: {password}")
        
        logger.info(f"  redis_db: {getattr(config, 'redis_db', 'NOT_SET')}")
        logger.info(f"  redis_max_connections: {getattr(config, 'redis_max_connections', 'NOT_SET')}")
        logger.info(f"  redis_socket_timeout: {getattr(config, 'redis_socket_timeout', 'NOT_SET')}")
        logger.info(f"  redis_socket_connect_timeout: {getattr(config, 'redis_socket_connect_timeout', 'NOT_SET')}")
        
        # 检查必要配置
        required_configs = ['redis_host', 'redis_port', 'redis_password']
        missing_configs = []
        
        for config_name in required_configs:
            value = getattr(config, config_name, None)
            if not value:
                missing_configs.append(config_name)
        
        if missing_configs:
            logger.error(f"缺少必要配置: {missing_configs}")
            return False
        else:
            logger.success("所有必要配置都已设置")
            return True
            
    except Exception as e:
        logger.error(f"配置调试异常: {e}")
        import traceback
        logger.error(f"异常详情: {traceback.format_exc()}")
        return False


def test_network_connectivity():
    """测试网络连通性"""
    logger.info("=== 网络连通性测试 ===")
    
    try:
        import socket
        from src.shared.config.environments import env_manager
        
        config = env_manager.get_config()
        host = getattr(config, 'redis_host', 'localhost')
        port = getattr(config, 'redis_port', 6379)
        
        logger.info(f"测试连接到: {host}:{port}")
        
        # 创建socket连接测试
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10秒超时
        
        try:
            result = sock.connect_ex((host, port))
            if result == 0:
                logger.success(f"网络连接成功: {host}:{port}")
                return True
            else:
                logger.error(f"网络连接失败: {host}:{port}, 错误码: {result}")
                return False
        finally:
            sock.close()
            
    except Exception as e:
        logger.error(f"网络连通性测试异常: {e}")
        return False


def test_redis_with_raw_client():
    """使用原生redis客户端测试"""
    logger.info("=== 原生Redis客户端测试 ===")
    
    try:
        import redis
        from src.shared.config.environments import env_manager
        
        config = env_manager.get_config()
        host = getattr(config, 'redis_host', 'localhost')
        port = getattr(config, 'redis_port', 6379)
        password = getattr(config, 'redis_password', '')
        db = getattr(config, 'redis_db', 0)
        
        logger.info(f"使用原生redis客户端连接: {host}:{port}")
        
        # 创建Redis客户端
        r = redis.Redis(
            host=host,
            port=port,
            password=password if password else None,
            db=db,
            socket_timeout=5,
            socket_connect_timeout=5,
            decode_responses=True
        )
        
        # 测试ping
        logger.info("执行PING命令...")
        result = r.ping()
        
        if result:
            logger.success("PING成功，Redis连接正常")
            
            # 测试基本操作
            test_key = "debug:test"
            test_value = "Hello from debug script"
            
            logger.info("测试SET/GET操作...")
            r.set(test_key, test_value, ex=60)
            retrieved_value = r.get(test_key)
            
            if retrieved_value == test_value:
                logger.success("SET/GET操作成功")
                r.delete(test_key)  # 清理
                return True
            else:
                logger.error(f"SET/GET操作失败: 期望 {test_value}, 实际 {retrieved_value}")
                return False
        else:
            logger.error("PING失败")
            return False
            
    except Exception as e:
        logger.error(f"原生Redis客户端测试异常: {e}")
        import traceback
        logger.error(f"异常详情: {traceback.format_exc()}")
        return False


def main():
    """主函数"""
    logger.info("开始Redis配置和连接调试...")
    
    # 1. 配置调试
    logger.info("\n" + "="*50)
    config_ok = debug_redis_config()
    
    if not config_ok:
        logger.error("配置检查失败，停止后续测试")
        return False
    
    # 2. 网络连通性测试
    logger.info("\n" + "="*50)
    network_ok = test_network_connectivity()
    
    if not network_ok:
        logger.warning("网络连通性测试失败，可能是网络限制")
        logger.info("这在本地开发环境中是正常的，因为阿里云Redis通常只允许内网访问")
        return False
    
    # 3. 原生客户端测试
    logger.info("\n" + "="*50)
    redis_ok = test_redis_with_raw_client()
    
    if redis_ok:
        logger.success("🎉 Redis连接和操作测试全部通过！")
        return True
    else:
        logger.error("❌ Redis连接测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        
        if not success:
            logger.info("\n" + "="*50)
            logger.info("调试建议:")
            logger.info("1. 确认Redis实例是否允许当前IP访问（白名单设置）")
            logger.info("2. 确认网络连通性（VPC/安全组配置）")
            logger.info("3. 确认用户名和密码是否正确")
            logger.info("4. 在实际部署环境中测试（如ECS实例）")
        
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("调试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"调试过程中发生异常: {e}")
        sys.exit(1)
