#!/usr/bin/env python3
"""
测试Group ID生成功能
验证在daily环境下是否正确添加UUID后缀
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.domain.services.group_manager import RocketMQGroupManager
from src.shared.config.environments import env_manager, Environment


def test_group_id_generation():
    """测试Group ID生成功能"""
    logger.info("=== 测试Group ID生成功能 ===")
    
    try:
        # 创建RocketMQGroupManager实例
        # 创建一个测试配置
        test_config = {
            "instance_id": "test_instance_id",
            "region": "cn-hangzhou",
            "access_key": "test_access_key",
            "secret_key": "test_secret_key"
        }
        group_manager = RocketMQGroupManager(test_config)
        
        # 测试当前环境（应该是daily）
        current_env = env_manager.current_env.value
        logger.info(f"当前环境: {current_env}")
        
        # 生成Group ID
        prefix = "test_group"
        group_id = group_manager.generate_unique_group_id(prefix)
        
        logger.info(f"生成的Group ID: {group_id}")
        
        # 验证Group ID格式
        parts = group_id.split("_")
        logger.info(f"Group ID组成部分: {parts}")
        
        if current_env == "daily":
            # daily环境应该有UUID后缀
            if len(parts) >= 3:  # prefix + ip + uuid
                uuid_suffix = parts[-1]
                if len(uuid_suffix) == 5:
                    logger.success(f"✅ Daily环境UUID后缀正确: {uuid_suffix} (长度: {len(uuid_suffix)})")
                else:
                    logger.error(f"❌ Daily环境UUID后缀长度错误: {uuid_suffix} (长度: {len(uuid_suffix)})")
                    return False
            else:
                logger.error(f"❌ Daily环境Group ID格式错误: {group_id}")
                return False
        else:
            # 非daily环境不应该有UUID后缀
            logger.info(f"非Daily环境，Group ID: {group_id}")
        
        # 测试多次生成，确保UUID后缀不同
        if current_env == "daily":
            logger.info("测试多次生成UUID后缀的唯一性...")
            group_ids = []
            for i in range(5):
                gid = group_manager.generate_unique_group_id(prefix)
                group_ids.append(gid)
                logger.info(f"第{i+1}次生成: {gid}")
            
            # 检查UUID后缀是否不同
            uuid_suffixes = [gid.split("_")[-1] for gid in group_ids]
            unique_suffixes = set(uuid_suffixes)
            
            if len(unique_suffixes) == len(uuid_suffixes):
                logger.success("✅ UUID后缀唯一性测试通过")
            else:
                logger.warning(f"⚠️ UUID后缀可能重复: {uuid_suffixes}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试Group ID生成异常: {e}")
        import traceback
        logger.error(f"异常详情: {traceback.format_exc()}")
        return False


def test_different_environments():
    """测试不同环境下的Group ID生成"""
    logger.info("=== 测试不同环境下的Group ID生成 ===")
    
    try:
        # 创建测试配置
        test_config = {
            "instance_id": "test_instance_id",
            "region": "cn-hangzhou",
            "access_key": "test_access_key",
            "secret_key": "test_secret_key"
        }
        group_manager = RocketMQGroupManager(test_config)
        prefix = "test_env"
        
        # 测试各个环境
        environments = [Environment.DAILY, Environment.PRE, Environment.PROD]
        
        for env in environments:
            logger.info(f"\n--- 测试 {env.value} 环境 ---")
            
            # 切换环境
            env_manager.switch_environment(env)
            
            # 生成Group ID
            group_id = group_manager.generate_unique_group_id(prefix)
            logger.info(f"{env.value} 环境生成的Group ID: {group_id}")
            
            # 验证格式
            parts = group_id.split("_")
            if env == Environment.DAILY:
                if len(parts) >= 3 and len(parts[-1]) == 5:
                    logger.success(f"✅ {env.value} 环境格式正确（包含UUID后缀）")
                else:
                    logger.error(f"❌ {env.value} 环境格式错误")
                    return False
            else:
                if len(parts) >= 2 and not (len(parts) >= 3 and len(parts[-1]) == 5):
                    logger.success(f"✅ {env.value} 环境格式正确（不包含UUID后缀）")
                else:
                    logger.warning(f"⚠️ {env.value} 环境可能包含了不应该有的UUID后缀")
        
        # 切换回daily环境
        env_manager.switch_environment(Environment.DAILY)
        logger.info("\n已切换回 daily 环境")
        
        return True
        
    except Exception as e:
        logger.error(f"测试不同环境异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始测试Group ID生成功能...")
    
    success_count = 0
    total_tests = 2
    
    # 测试基本功能
    if test_group_id_generation():
        success_count += 1
        logger.success("✅ Group ID生成测试通过")
    else:
        logger.error("❌ Group ID生成测试失败")
    
    # 测试不同环境
    if test_different_environments():
        success_count += 1
        logger.success("✅ 不同环境测试通过")
    else:
        logger.error("❌ 不同环境测试失败")
    
    # 总结
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有Group ID生成测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
