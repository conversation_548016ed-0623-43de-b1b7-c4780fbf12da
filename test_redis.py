#!/usr/bin/env python3
"""
Redis功能测试脚本
用于测试Redis连接和基本操作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.infrastructure.redis import redis_manager, RedisClient


def test_redis_connection():
    """测试Redis连接"""
    logger.info("=== 测试Redis连接 ===")
    
    try:
        # 初始化Redis管理器
        redis_manager.initialize()
        
        # 获取连接信息
        conn_info = redis_manager.get_connection_info()
        logger.info(f"连接信息: {conn_info}")
        
        # 健康检查
        health = redis_manager.health_check()
        logger.info(f"健康检查: {'通过' if health else '失败'}")
        
        if health:
            logger.success("Redis连接测试成功！")
            return True
        else:
            logger.error("Redis连接测试失败！")
            return False
            
    except Exception as e:
        logger.error(f"Redis连接测试异常: {e}")
        return False


def test_redis_operations():
    """测试Redis基本操作"""
    logger.info("=== 测试Redis基本操作 ===")
    
    try:
        redis_client = RedisClient()
        
        # 测试字符串操作
        logger.info("测试字符串操作...")
        redis_client.set("test:string", "Hello Redis!")
        value = redis_client.get("test:string")
        assert value == "Hello Redis!", f"字符串操作失败: {value}"
        logger.success("字符串操作测试通过")
        
        # 测试JSON对象
        logger.info("测试JSON对象...")
        test_obj = {"name": "测试", "value": 123, "list": [1, 2, 3]}
        redis_client.set("test:json", test_obj)
        retrieved_obj = redis_client.get("test:json")
        assert retrieved_obj == test_obj, f"JSON对象操作失败: {retrieved_obj}"
        logger.success("JSON对象操作测试通过")
        
        # 测试哈希操作
        logger.info("测试哈希操作...")
        hash_data = {"field1": "value1", "field2": 42, "field3": [1, 2, 3]}
        redis_client.hset("test:hash", hash_data)
        retrieved_hash = redis_client.hgetall("test:hash")
        assert retrieved_hash == hash_data, f"哈希操作失败: {retrieved_hash}"
        logger.success("哈希操作测试通过")
        
        # 测试列表操作
        logger.info("测试列表操作...")
        redis_client.rpush("test:list", "item1", "item2", "item3")
        list_len = redis_client.llen("test:list")
        assert list_len == 3, f"列表长度错误: {list_len}"
        
        popped_item = redis_client.lpop("test:list")
        assert popped_item == "item1", f"列表弹出错误: {popped_item}"
        logger.success("列表操作测试通过")
        
        # 测试集合操作
        logger.info("测试集合操作...")
        redis_client.sadd("test:set", "member1", "member2", "member3")
        set_size = redis_client.scard("test:set")
        assert set_size == 3, f"集合大小错误: {set_size}"
        
        members = redis_client.smembers("test:set")
        expected_members = {"member1", "member2", "member3"}
        assert members == expected_members, f"集合成员错误: {members}"
        logger.success("集合操作测试通过")
        
        # 测试过期时间
        logger.info("测试过期时间...")
        redis_client.set("test:expire", "will expire", ex=2)
        exists_before = redis_client.exists("test:expire")
        assert exists_before, "过期键应该存在"
        
        ttl = redis_client.ttl("test:expire")
        assert ttl > 0, f"TTL应该大于0: {ttl}"
        logger.success("过期时间测试通过")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        test_keys = redis_client.keys("test:*")
        if test_keys:
            redis_client.delete(*test_keys)
            logger.info(f"已清理 {len(test_keys)} 个测试键")
        
        logger.success("Redis基本操作测试全部通过！")
        return True
        
    except Exception as e:
        logger.error(f"Redis操作测试异常: {e}")
        return False


def test_redis_performance():
    """测试Redis性能"""
    logger.info("=== 测试Redis性能 ===")
    
    try:
        import time
        redis_client = RedisClient()
        
        # 批量写入测试
        logger.info("批量写入测试...")
        start_time = time.time()
        
        for i in range(1000):
            redis_client.set(f"perf:test:{i}", f"value_{i}")
        
        write_time = time.time() - start_time
        logger.info(f"写入1000个键耗时: {write_time:.3f}秒")
        
        # 批量读取测试
        logger.info("批量读取测试...")
        start_time = time.time()
        
        for i in range(1000):
            value = redis_client.get(f"perf:test:{i}")
            assert value == f"value_{i}", f"读取错误: {value}"
        
        read_time = time.time() - start_time
        logger.info(f"读取1000个键耗时: {read_time:.3f}秒")
        
        # 清理性能测试数据
        perf_keys = redis_client.keys("perf:test:*")
        if perf_keys:
            redis_client.delete(*perf_keys)
            logger.info(f"已清理 {len(perf_keys)} 个性能测试键")
        
        logger.success("Redis性能测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"Redis性能测试异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始Redis功能测试...")
    
    # 测试连接
    if not test_redis_connection():
        logger.error("Redis连接测试失败，退出测试")
        return False
    
    # 测试基本操作
    if not test_redis_operations():
        logger.error("Redis操作测试失败")
        return False
    
    # 测试性能
    if not test_redis_performance():
        logger.error("Redis性能测试失败")
        return False
    
    logger.success("所有Redis测试通过！")
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
