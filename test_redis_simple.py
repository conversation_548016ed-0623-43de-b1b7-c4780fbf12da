#!/usr/bin/env python3
"""
简单Redis连接测试
直接使用阿里云Redis连接示例格式
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger
from src.infrastructure.redis import redis_manager, RedisClient


def test_direct_connection():
    """直接测试Redis连接"""
    logger.info("=== 直接测试Redis连接 ===")
    
    try:
        # 测试连接管理器
        logger.info("1. 测试Redis连接管理器...")
        redis_manager.initialize()
        
        # 获取连接信息
        conn_info = redis_manager.get_connection_info()
        logger.info(f"连接信息: {conn_info}")
        
        # 健康检查
        health = redis_manager.health_check()
        logger.info(f"健康检查: {'通过' if health else '失败'}")
        
        if not health:
            logger.error("Redis连接失败")
            return False
        
        # 测试基本操作
        logger.info("2. 测试基本Redis操作...")
        redis_client = RedisClient()
        
        # 测试SET和GET
        test_key = "test:connection"
        test_value = "Hello Redis from Alpha Service!"
        
        logger.info(f"设置键值: {test_key} = {test_value}")
        success = redis_client.set(test_key, test_value, ex=60)  # 60秒过期
        
        if success:
            logger.success("SET操作成功")
            
            # 获取值
            retrieved_value = redis_client.get(test_key)
            logger.info(f"获取到的值: {retrieved_value}")
            
            if retrieved_value == test_value:
                logger.success("GET操作成功，值匹配")
                
                # 测试TTL
                ttl = redis_client.ttl(test_key)
                logger.info(f"键的剩余生存时间: {ttl}秒")
                
                # 清理测试数据
                redis_client.delete(test_key)
                logger.info("测试数据已清理")
                
                return True
            else:
                logger.error(f"值不匹配: 期望 {test_value}, 实际 {retrieved_value}")
                return False
        else:
            logger.error("SET操作失败")
            return False
            
    except Exception as e:
        logger.error(f"Redis连接测试异常: {e}")
        import traceback
        logger.error(f"异常详情: {traceback.format_exc()}")
        return False


def test_json_operations():
    """测试JSON数据操作"""
    logger.info("=== 测试JSON数据操作 ===")
    
    try:
        redis_client = RedisClient()
        
        # 测试复杂JSON对象
        test_data = {
            "user_id": "12345",
            "name": "张三",
            "email": "<EMAIL>",
            "preferences": {
                "language": "zh-CN",
                "theme": "dark"
            },
            "tags": ["Python", "Redis", "FastAPI"],
            "created_at": "2025-01-20T10:00:00Z"
        }
        
        key = "test:user:12345"
        logger.info(f"存储JSON对象: {key}")
        
        success = redis_client.set(key, test_data, ex=300)  # 5分钟过期
        
        if success:
            logger.success("JSON对象存储成功")
            
            # 获取JSON对象
            retrieved_data = redis_client.get(key)
            logger.info(f"获取到的JSON对象: {retrieved_data}")
            
            if retrieved_data == test_data:
                logger.success("JSON对象序列化/反序列化成功")
                
                # 清理
                redis_client.delete(key)
                return True
            else:
                logger.error("JSON对象不匹配")
                return False
        else:
            logger.error("JSON对象存储失败")
            return False
            
    except Exception as e:
        logger.error(f"JSON操作测试异常: {e}")
        return False


def test_hash_operations():
    """测试哈希操作"""
    logger.info("=== 测试哈希操作 ===")
    
    try:
        redis_client = RedisClient()
        
        # 测试哈希数据
        hash_key = "test:session:abc123"
        session_data = {
            "user_id": "12345",
            "login_time": "2025-01-20T10:00:00Z",
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "permissions": ["read", "write", "admin"]
        }
        
        logger.info(f"存储哈希数据: {hash_key}")
        
        # 设置哈希
        count = redis_client.hset(hash_key, session_data)
        logger.info(f"设置了 {count} 个哈希字段")
        
        # 获取单个字段
        user_id = redis_client.hget(hash_key, "user_id")
        logger.info(f"获取用户ID: {user_id}")
        
        # 获取所有字段
        all_data = redis_client.hgetall(hash_key)
        logger.info(f"获取所有哈希数据: {all_data}")
        
        # 检查关键字段是否存在和正确（考虑类型转换）
        user_id_match = (str(all_data.get("user_id")) == str(session_data["user_id"]))
        login_time_match = (all_data.get("login_time") == session_data["login_time"])
        ip_match = (all_data.get("ip_address") == session_data["ip_address"])

        if user_id_match and login_time_match and ip_match:
            logger.success("哈希操作成功")

            # 清理
            redis_client.delete(hash_key)
            return True
        else:
            logger.error(f"哈希数据不匹配")
            logger.error(f"user_id匹配: {user_id_match} (期望: {session_data['user_id']}, 实际: {all_data.get('user_id')})")
            logger.error(f"login_time匹配: {login_time_match}")
            logger.error(f"ip_address匹配: {ip_match}")
            return False
            
    except Exception as e:
        logger.error(f"哈希操作测试异常: {e}")
        return False


def main():
    """主函数"""
    logger.info("开始Redis简单连接测试...")
    
    success_count = 0
    total_tests = 3
    
    # 测试连接
    if test_direct_connection():
        success_count += 1
        logger.success("✅ 连接测试通过")
    else:
        logger.error("❌ 连接测试失败")
    
    # 测试JSON操作
    if test_json_operations():
        success_count += 1
        logger.success("✅ JSON操作测试通过")
    else:
        logger.error("❌ JSON操作测试失败")
    
    # 测试哈希操作
    if test_hash_operations():
        success_count += 1
        logger.success("✅ 哈希操作测试通过")
    else:
        logger.error("❌ 哈希操作测试失败")
    
    # 总结
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        logger.success("🎉 所有Redis测试通过！")
        return True
    else:
        logger.error(f"❌ {total_tests - success_count} 个测试失败")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        sys.exit(1)
